
import { MessageCircle, Phone } from 'lucide-react';

interface SupportIconProps {
  onClick: () => void;
}

const SupportIcon = ({ onClick }: SupportIconProps) => {
  return (
    <div 
      className="fixed bottom-6 right-6 z-50 cursor-pointer group"
      onClick={onClick}
      role="button"
      aria-label="Open Chat and Call Support"
      tabIndex={0}
      onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && onClick()}
    >
      <div className="relative">
        {/* Main Icon */}
        <div className="w-16 h-16 bg-gradient-to-r from-ai-cyan to-ai-purple rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl animate-glow-pulse">
          <div className="relative">
            <MessageCircle className="w-6 h-6 text-white" />
            <Phone className="w-3 h-3 text-white absolute -top-1 -right-1" />
          </div>
        </div>
        
        {/* AI-inspired accents */}
        <div className="absolute -top-1 -left-1 w-4 h-4 border-2 border-ai-cyan rounded-full opacity-60 animate-ping"></div>
        <div className="absolute -bottom-1 -right-1 w-3 h-3 border border-ai-purple rounded-full opacity-40 animate-pulse"></div>
        
        {/* Neural network lines */}
        <div className="absolute top-2 left-2 w-1 h-1 bg-ai-cyan rounded-full opacity-70"></div>
        <div className="absolute bottom-3 right-2 w-1 h-1 bg-ai-purple rounded-full opacity-70"></div>
      </div>
      
      {/* Tooltip */}
      <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-background border border-border rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
        <span className="text-sm text-foreground">Chat & Call Support</span>
        <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-border"></div>
      </div>
    </div>
  );
};

export default SupportIcon;
