using System.Collections.Generic;

namespace RecoveryRx.Server.DTOs.Database.Responses
{
    /// <summary>
    /// Represents the response for the database initialization request.
    /// </summary>
    public class InitializeDatabaseResponse
    {
        /// <summary>
        /// Gets or sets a message indicating the outcome of the initialization.
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the name of the database provider used for initialization.
        /// </summary>
        public string? ProviderName { get; set; }

        /// <summary>
        /// Gets or sets details about the database that was initialized (e.g., database name or file path).
        /// </summary>
        public string? DatabaseDetails { get; set; }

        /// <summary>
        /// Gets or sets a list of actions performed during initialization (e.g., "Database created", "Migrations applied").
        /// </summary>
        public List<string>? ActionsPerformed { get; set; }
    }
}
