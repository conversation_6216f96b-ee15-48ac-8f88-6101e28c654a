
import { useState } from 'react';
import SupportIcon from './SupportIcon';
import UserInfoPanel, { UserInfo } from './UserInfoPanel';
import ChatInterface from './ChatInterface';
import LiveCallInterface from './LiveCallInterface';

type WidgetState = 'closed' | 'userInfo' | 'chat' | 'call';

const SupportWidget = () => {
  const [state, setState] = useState<WidgetState>('closed');
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  const handleIconClick = () => {
    setState('userInfo');
  };

  const handleStartChat = (info: UserInfo) => {
    setUserInfo(info);
    setState('chat');
  };

  const handleStartCall = (info: UserInfo) => {
    setUserInfo(info);
    setState('call');
  };

  const handleClose = () => {
    setState('closed');
    setUserInfo(null);
  };

  return (
    <>
      <SupportIcon onClick={handleIconClick} />
      
      {state === 'userInfo' && (
        <UserInfoPanel
          onStartChat={handleStartChat}
          onStartCall={handleStartCall}
          onClose={handleClose}
        />
      )}
      
      {state === 'chat' && userInfo && (
        <ChatInterface
          userInfo={userInfo}
          onClose={handleClose}
        />
      )}
      
      {state === 'call' && userInfo && (
        <LiveCallInterface
          userInfo={userInfo}
          onClose={handleClose}
        />
      )}
    </>
  );
};

export default SupportWidget;
