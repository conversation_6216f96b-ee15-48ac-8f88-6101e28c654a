import { Box, Container, Grid, Typography } from '@mui/material';
import AnimatedContent from './AnimatedContent';

const textItems: { type: string; content: string | string[] }[] = [
  { type: 'heading', content: 'Who We Are' },
  { type: 'paragraph', content: 'Benza Advertising is a creative agency dedicated to helping small businesses grow through powerful visuals and strategic marketing. Founded by Be<PERSON><PERSON> G<PERSON>i, we specialize in photography, graphic design, and digital advertising to create eye-catching campaigns that attract customers and build brand trust.' },
  { type: 'heading', content: 'How We Help Small Businesses' },
  { type: 'paragraph', content: 'We understand that small businesses often face challenges like limited budgets, time constraints, and brand visibility. That’s why we offer affordable, high-quality marketing solutions tailored to their unique needs.' },
  { type: 'list', content: [
    'Professional yet cost-effective branding to make your business stand out',
    'High-quality photography and advertising that elevates your brand image',
    'AI-driven marketing strategies to help you reach the right audience efficiently',
    'Personalized support and consultation to ensure your success',
  ]},
  { type: 'italic', content: 'At Benza Advertising, we don’t just create visuals—we create growth opportunities for small businesses.' },
  { type: 'button', content: 'Let’s grow your business together!' },
];

const AboutUsSection: React.FC = () => {
  return (
    <Box sx={{ py: 8, bgcolor: 'background.paper' }}>
      <Container maxWidth="lg">
        <Typography variant="h2" component="h2" align="center" sx={{ mb: 6, color: 'primary.main' }}>
          A Digital Partnership for Businesses That Dream Big
        </Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Box
              component="img"
              src="/benza_founder.jpg"
              alt="About Benza Advertising"
              sx={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 2 }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <AnimatedContent textItems={textItems} />
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default AboutUsSection;