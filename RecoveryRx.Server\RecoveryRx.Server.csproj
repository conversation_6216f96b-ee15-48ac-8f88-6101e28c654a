<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.13">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.13">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.11.0" />
  </ItemGroup>



  <ItemGroup>
    <ProjectReference Include="..\Migrations\RecoveryRx.Sqlite\RecoveryRx.Sqlite.csproj" />
    <ProjectReference Include="..\Migrations\RecoveryRx.MySql\RecoveryRx.MySql.csproj" />
    <ProjectReference Include="..\RecoveryRx.Models\RecoveryRx.Models.csproj" />
  </ItemGroup>



  <ItemGroup>
    <Compile Update="Resources\SharedResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SharedResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>



  <ItemGroup>
    <EmbeddedResource Update="Resources\SharedResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SharedResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
</Project>
