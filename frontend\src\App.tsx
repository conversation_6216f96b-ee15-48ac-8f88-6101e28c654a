import { Routes, Route, Navigate } from 'react-router-dom'
import './App.css'
import Login from './pages/Login'
import Register from './pages/Register'
import Home from './pages/Home'
import { useAuth } from './contexts/AuthContext'


const App = () => {
  const { isAuthenticated } = useAuth();
  console.log("routes", { isAuthenticated });

  return (
    <Routes>
      <Route path="/login" element={isAuthenticated ? <Navigate to="/" /> : <Login />} />
      <Route path="/register" element={isAuthenticated ? <Navigate to="/" /> : <Register />} />
      <Route path="/" element={isAuthenticated ? <Home /> : <Navigate to="/login" />} />
    </Routes>
  );
};

export default App
