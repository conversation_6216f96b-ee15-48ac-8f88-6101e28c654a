"use client";
import { createTheme } from "@mui/material/styles";

const theme = createTheme({
  palette: {
    primary: {
      main: "#006674", // Teal
      light: "#3E8B95",
      dark: "#004F5A",
      contrastText: "#FFFFFF",
    },
    secondary: {
      main: "#00E4B2", // Mint green
      light: "#4EEDCA",
      dark: "#00B890",
      contrastText: "#002017",
    },
    background: {
      default: "#FFFFFF",
      paper: "#F8F8F8",
      dark: "#002017", // Custom property
    },
    text: {
      primary: "#212121",
      secondary: "#757575",
      light: "#FFFFFF", // Custom property
    },
    success: {
      main: "#5CBBA5",
    },
    error: {
      main: "#FF5252",
    },
  },
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: { fontWeight: 700 },
    h2: { fontWeight: 700, fontSize: "2.5rem" },
    h3: { fontWeight: 600 },
    h4: { fontWeight: 600 },
    h5: { fontWeight: 500 },
    h6: { fontWeight: 500 },
    button: { fontWeight: 600, textTransform: "none" },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: { borderRadius: 8, padding: "10px 20px" },
        containedPrimary: { "&:hover": { backgroundColor: "#004F5A" } },
        containedSecondary: { "&:hover": { backgroundColor: "#00B890" } },
      },
    },
    MuiCard: { styleOverrides: { root: { borderRadius: 8 } } },
    MuiTextField: {
      styleOverrides: {
        root: { "& .MuiOutlinedInput-root": { borderRadius: 8 } },
      },
    },
  },
});

declare module "@mui/material/styles" {
  interface TypeBackground {
    dark: string;
  }

  interface TypeText {
    light: string;
  }

  interface Palette {
    background: TypeBackground;
    text: TypeText;
  }

  interface PaletteOptions {
    background?: Partial<TypeBackground>;
    text?: Partial<TypeText>;
  }
}

export default theme;
