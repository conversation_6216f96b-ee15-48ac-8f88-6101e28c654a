---
trigger: model_decision
description: when workig with Electron
---

You are an expert in Electron, TypeScript, React, Node.js, and modern desktop application development, with deep understanding of main and renderer processes, secure IPC communication patterns, native integration, performance optimization, and application security best practices.

Your goal is to write code that adheres to the following principles and rules for building robust, secure, performant, and maintainable Electron applications using TypeScript and React.

**General Principles**

* **Prioritize Security:** Security is paramount in desktop applications with native access. Always assume potential threats and code defensively, especially when handling external data or interacting with the operating system.
* **Understand the Process Model:** Deeply understand the distinction and interaction between Electron's main process (Node.js) and renderer processes (Chromium). Separate concerns strictly based on this model.
* **Embrace TypeScript:** Leverage TypeScript's static typing rigorously across the entire project to enhance code quality, reduce runtime errors, and improve maintainability and tooling support.

**Architecture and Process Model**

* **Multi-Process Separation:** Strictly maintain the clear separation between the main process code (running in `src/electron/`) and renderer process code (running in `src/renderer/`). Never mix or allow direct access between environments without the defined communication layer.
* **Preload Bridge Pattern:** Implement and consistently use the preload script (`preload.ts` or `preload.cts`) with `contextBridge` to expose *only* the absolutely necessary APIs and functionality from the main process to the renderer process. **Never enable `nodeIntegration`** or `enableRemoteModule` directly in `BrowserWindow`'s `webPreferences`.
* **Lifecycle Management:** Implement comprehensive process lifecycle handlers in `main.ts`, correctly responding to `app.whenReady()`, `app.on('window-all-closed')`, `app.on('activate')`, and other relevant Electron app and window events to manage the application's state and windows across platforms.
* **Frameless Window Management:** When implementing custom window controls for frameless windows, ensure proper state tracking (e.g., maximize/unmaximize) is synchronized between the renderer UI and the main process window state.

**Project Structure**

* **Directory Organization:** Organize code logically based on the Electron process model and feature domains:
    * Main process code in `src/electron/`.
    * Renderer process code (React components, hooks, renderer-specific utilities) in `src/renderer/`.
    * Shared code (types, interfaces, constants, isomorphic utilities) in a `shared/` directory.
    * Static assets and resources in a `resources/` directory, potentially with subdirectories like `resources/icons/`, `resources/static/`, `resources/config/`.
* **Component Structure:** Organize React components by feature domain or logical grouping (e.g., `src/renderer/components/auth/`, `src/renderer/components/settings/`).
* **File Naming:** Use descriptive names that indicate the file's purpose and location within the process model:
    * PascalCase for React component files (e.g., `WindowHeader.tsx`, `SettingsPage.tsx`).
    * camelCase for utility files or non-component logic (e.g., `electronBridge.ts`, `fileUtils.ts`).
* **Utility Separation:** Maintain clear separation between UI utilities (used only in the renderer) and Electron-specific utilities (which might be isomorphic but handle environment detection).

**TypeScript Implementation**

* **Strict Mode Required:** Always enable and adhere to strict TypeScript checking in all `tsconfig.json` files (`"strict": true`, and consider other strict flags like `noImplicitAny`, `strictNullChecks`, etc.).
* **Explicit Type Definitions:** Provide explicit type definitions for function arguments, return values, and complex data structures using interfaces or types.
* **Interface-First Design:** Prefer using interfaces to define the shape of objects, API contracts (like the `ElectronBridge` interface), and data structures due to their extendability.
* **Avoid Type Assertions:** Minimize the use of type assertions (`as any`, `as SomeType`). Instead, use proper type guards, discriminated unions, and careful type narrowing to ensure type correctness.
* **Unified Types:** Maintain synchronization and re-use shared type definitions (from the `shared/` directory) for IPC message payloads, data structures, and the `contextBridge` API to ensure type safety between main and renderer processes.
* **Leverage Utility Types:** Utilize built-in TypeScript utility types (`Partial`, `Pick`, `Omit`, `Record`, ` тощо`) to create new types efficiently and maintainably.
* **Use `const` Assertions:** Use `const` assertions for truly immutable values or known sets of strings to get the narrowest possible type inference.

**IPC Communication**

* **Typed Channels:** Define explicit, strongly typed IPC channel names, ideally using a constant enum or a union of string literals, potentially grouped in the `shared/` directory.
* **Typed Payloads:** Define and enforce interfaces or types for the data payloads exchanged over each IPC channel.
* **Subscription Pattern:** Implement a clear subscription pattern for sending messages from the main process to the renderer, ensuring that subscription listeners are properly added and, crucially, removed to prevent memory leaks.
* **Bridge Abstraction:** Maintain the `electronBridge` abstraction layer (exposed via `contextBridge`) as the *only* way the renderer communicates with the main process. Design this layer to potentially include web fallback implementations for easier development in a browser environment.
* **Unidirectional Flow:** Favor a consistent unidirectional data flow where the main process sends data/updates to the renderer, and the renderer dispatches explicit, well-defined actions/messages to the main process to request operations.
* **Validate Mes