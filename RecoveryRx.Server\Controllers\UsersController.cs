using Microsoft.AspNetCore.Mvc;
using RecoveryRx.Server.DTOs.User;
using RecoveryRx.Server.DTOs.UserRole;
using RecoveryRx.Server.Services;
using System;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly IUserService _userService;

        public UsersController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllUsers()
        {
            var users = await _userService.GetAllUsersAsync();
            return Ok(users);
        }

        [HttpGet("{id:guid}")]
        public async Task<IActionResult> GetUserById(Guid id)
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                return NotFound($"User with ID {id} not found.");
            }
            return Ok(user);
        }

        [HttpGet("username/{username}")]
        public async Task<IActionResult> GetUserByUsername(string username)
        {
            var user = await _userService.GetUserByUsernameAsync(username);
            if (user == null)
            {
                return NotFound($"User with username '{username}' not found.");
            }
            return Ok(user);
        }

        [HttpPost]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserDto createUserDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var (user, errorMessage) = await _userService.CreateUserAsync(createUserDto);
            if (user == null)
            {
                return BadRequest(errorMessage ?? "Failed to create user.");
            }
            // Return 201 Created with a link to the new resource and the resource itself
            return CreatedAtAction(nameof(GetUserById), new { id = user.Id }, user);
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateUser(Guid id, [FromBody] UpdateUserDto updateUserDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var (success, errorMessage) = await _userService.UpdateUserAsync(id, updateUserDto);
            if (!success)
            {
                // Distinguish between "not found" and other update failures
                if (errorMessage != null && errorMessage.Contains("not found")) 
                    return NotFound(errorMessage);
                return BadRequest(errorMessage ?? "Failed to update user.");
            }
            return NoContent(); // Or Ok(updatedUserDto) if you return the updated DTO
        }

        [HttpDelete("{id:guid}")]
        public async Task<IActionResult> DeleteUser(Guid id)
        {
            var success = await _userService.DeleteUserAsync(id);
            if (!success)
            {
                return NotFound($"User with ID {id} not found or could not be deleted.");
            }
            return NoContent();
        }

        [HttpPost("assign-role")]
        public async Task<IActionResult> AssignRoleToUser([FromBody] AssignRoleToUserDto assignRoleDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var (success, errorMessage) = await _userService.AssignRoleToUserAsync(assignRoleDto);
            if (!success)
            {
                return BadRequest(errorMessage ?? "Failed to assign role to user.");
            }
            return Ok("Role assigned successfully.");
        }

        [HttpPost("remove-role")] // Using POST for consistency, could be DELETE with a more complex route
        public async Task<IActionResult> RemoveRoleFromUser([FromBody] AssignRoleToUserDto removeRoleDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var (success, errorMessage) = await _userService.RemoveRoleFromUserAsync(removeRoleDto);
            if (!success)
            {
                return BadRequest(errorMessage ?? "Failed to remove role from user.");
            }
            return Ok("Role removed successfully.");
        }
    }
}
