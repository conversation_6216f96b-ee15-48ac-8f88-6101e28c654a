using Microsoft.EntityFrameworkCore;
using RecoveryRx.Models;
 

namespace RecoveryRx.Server.Data.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly RecoveryRxContext _context;

        public UserRepository(RecoveryRxContext context)
        {
            _context = context;
        }

        public async Task<User?> GetUserByIdAsync(Guid id)
        {
            return await _context.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Id == id);
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            return await _context.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Username == username);
        }
        
        public async Task<User?> GetUserByEmailAsync(string email)
        {
            return await _context.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.<PERSON>)
                .FirstOrDefaultAsync(u => u.Email == email);
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return await _context.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .ToListAsync();
        }

        public async Task<User> CreateUserAsync(User user, string password)
        {
            // Password hashing should be done in the service layer before this method is called
            // This repository method assumes the user.PasswordHash is already set.
            _context.Users.Add(user);
            await _context.SaveChangesAsync();
            return user;
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            var existingUser = await _context.Users.FindAsync(user.Id);
            if (existingUser == null) return false;

            _context.Entry(existingUser).CurrentValues.SetValues(user);
            // For more complex updates, especially with navigation properties,
            // you might need more specific logic here.
            // For example, if IsActive is changed, or if roles are managed directly here (not recommended).
            existingUser.UpdatedAt = DateTime.UtcNow;
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteUserAsync(Guid id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null) return false;

            _context.Users.Remove(user);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> AssignRoleToUserAsync(Guid userId, string roleName)
        {
            var user = await _context.Users.FindAsync(userId);
            var role = await _context.Roles.FirstOrDefaultAsync(r => r.Name == roleName);

            if (user == null || role == null) return false;

            var userRole = new UserRole { UserId = userId, RoleId = role.Id };
            if (!await _context.UserRoles.AnyAsync(ur => ur.UserId == userId && ur.RoleId == role.Id))
            {
                _context.UserRoles.Add(userRole);
                return await _context.SaveChangesAsync() > 0;
            }
            return false; // Role already assigned
        }

        public async Task<bool> RemoveRoleFromUserAsync(Guid userId, string roleName)
        {
            var user = await _context.Users.FindAsync(userId);
            var role = await _context.Roles.FirstOrDefaultAsync(r => r.Name == roleName);

            if (user == null || role == null) return false;

            var userRole = await _context.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == role.Id);

            if (userRole != null)
            {
                _context.UserRoles.Remove(userRole);
                return await _context.SaveChangesAsync() > 0;
            }
            return false; // Role not found for this user
        }
        
        public async Task<IEnumerable<string>> GetUserRolesAsync(Guid userId)
        {
            return await _context.UserRoles
                .Where(ur => ur.UserId == userId)
                .Include(ur => ur.Role)
                .Select(ur => ur.Role.Name)
                .ToListAsync();
        }

        public async Task<bool> UserExistsAsync(string username, string email)
        {
            return await _context.Users.AnyAsync(u => u.Username == username || u.Email == email);
        }
    }
}
