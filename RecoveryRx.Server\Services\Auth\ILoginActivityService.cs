using RecoveryRx.Models;
using RecoveryRx.Server.DTOs.Auth;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Services.Auth
{
    public interface ILoginActivityService
    {
        Task<LoginActivity> LogLoginAttemptAsync(Guid userId, string username, string ipAddress, string userAgent, bool success, string? failureReason = null);
        Task<IEnumerable<LoginActivityDto>> GetLoginHistoryForUserAsync(Guid userId, int limit = 10);
        Task<IEnumerable<LoginActivityDto>> GetRecentLoginAttemptsAsync(int limit = 50);
        Task CleanupOldLoginRecordsAsync(int daysToKeep = 90);
    }
}
