# RecoveryRx Build Script
# This script builds both the .NET server and Electron client and packages them together

$ErrorActionPreference = "Stop"

# Variables
$serverProjectPath = ".\RecoveryRx.Server\RecoveryRx.Server.csproj"
$clientProjectPath = ".\recoveryrx.client"
$serverOutputPath = ".\recoveryrx.client\resources\server\publish"
$buildOutputPath = ".\dist"

Write-Host "=== RecoveryRx Build Script ===" -ForegroundColor Cyan

# Step 1: Clean the output directories
Write-Host "`n[1/6] Cleaning output directories..." -ForegroundColor Yellow
if (Test-Path $serverOutputPath) {
    Remove-Item -Path $serverOutputPath -Recurse -Force
}
New-Item -Path $serverOutputPath -ItemType Directory -Force | Out-Null

# Also clean the dist directory
Push-Location $clientProjectPath
if (Test-Path "dist") {
    Write-Host "Cleaning previous build output..." -ForegroundColor Yellow
    Remove-Item -Path "dist" -Recurse -Force
}
Pop-Location

# Step 2: Build the .NET Server
Write-Host "`n[2/6] Building .NET Server..." -ForegroundColor Yellow
# dotnet publish $serverProjectPath -c Release -o $serverOutputPath --self-contained true -r win-x64 /p:PublishSingleFile=true /p:PublishTrimmed=true
dotnet publish $serverProjectPath -c Release -o $serverOutputPath --self-contained true -r win-x64 /p:PublishSingleFile=true

if (-not $?) {
    Write-Host "Error building .NET Server. Exiting." -ForegroundColor Red
    exit 1
}

Write-Host ".NET Server build completed successfully!" -ForegroundColor Green

# Step 3: Install Node.js dependencies for the Electron app
Write-Host "`n[3/6] Installing Node.js dependencies..." -ForegroundColor Yellow
Push-Location $clientProjectPath
npm install

if (-not $?) {
    Write-Host "Error installing Node.js dependencies. Exiting." -ForegroundColor Red
    Pop-Location
    exit 1
}

# Step 4: Build the Electron app
Write-Host "`n[4/6] Building Electron app..." -ForegroundColor Yellow
npm run build

if (-not $?) {
    Write-Host "Error building Electron app. Exiting." -ForegroundColor Red
    Pop-Location
    exit 1
}

# Step 5: Package the Electron app for Windows
Write-Host "`n[5/6] Packaging Electron app for Windows..." -ForegroundColor Yellow
npm run dist:win

if (-not $?) {
    Write-Host "Error packaging Electron app. Exiting." -ForegroundColor Red
    Pop-Location
    exit 1
}

# Step 6: Verify the build output
Write-Host "`n[6/6] Verifying build output..." -ForegroundColor Yellow

# Check if the installer was created
$installerPath = Join-Path -Path (Resolve-Path "dist").Path -ChildPath "*.exe"
$installers = Get-Item $installerPath -ErrorAction SilentlyContinue

if ($installers) {
    Write-Host "Build successful! Found installer(s):" -ForegroundColor Green
    $installers | ForEach-Object {
        Write-Host "  - $($_.Name)" -ForegroundColor Green
    }
} else {
    Write-Host "Warning: No installer found in the dist directory." -ForegroundColor Yellow
}

# Check for the unpacked app
$unpackedPath = Join-Path -Path (Resolve-Path "dist").Path -ChildPath "win-unpacked"
if (Test-Path $unpackedPath) {
    # Verify server files were included properly
    $serverFiles = Join-Path -Path $unpackedPath -ChildPath "server\publish\RecoveryRx.Server.exe"
    if (Test-Path $serverFiles) {
        Write-Host "Server executable found in the correct location." -ForegroundColor Green
    } else {
        Write-Host "Warning: Server executable not found in the expected location." -ForegroundColor Red
        Write-Host "Please check: $serverFiles" -ForegroundColor Yellow
    }
}

Pop-Location

Write-Host "`n=== Build Complete! ===" -ForegroundColor Cyan
Write-Host "You can find the installer in: $clientProjectPath\dist" -ForegroundColor Green
