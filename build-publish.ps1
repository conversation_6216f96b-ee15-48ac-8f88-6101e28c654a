# RecoveryRx Build Script
# This script builds both the .NET server and Electron client and packages them together
# It can also publish releases to GitHub for auto-updates

param (
    [switch]$PublishToGitHub,
    [string]$GitHubToken = $env:GH_TOKEN,
    [string]$ReleaseNotes = "New release",
    [switch]$Draft = $true,
    [switch]$Prerelease = $false
)

$ErrorActionPreference = "Stop"

# Variables
$serverProjectPath = ".\RecoveryRx.Server\RecoveryRx.Server.csproj"
$serverOutputPath = ".\recoveryrx.client\resources\server\publish"
$gitHubOwner = "keleshteri"
$gitHubRepo = "RecoveryRx-releases"
$gitHubApiUrl = "https://api.github.com"

# Get the absolute paths
$scriptDir = $PSScriptRoot
$absoluteClientPath = Join-Path -Path $scriptDir -ChildPath "recoveryrx.client"
$clientProjectPath = $absoluteClientPath

Write-Host "=== RecoveryRx Build Script ===" -ForegroundColor Cyan

# Function to get the current version from package.json
function Get-AppVersion {
    $packageJsonPath = Join-Path -Path $clientProjectPath -ChildPath "package.json"
    $packageJson = Get-Content -Path $packageJsonPath -Raw | ConvertFrom-Json
    return $packageJson.version
}

# Function to check if a GitHub release already exists
function Test-GitHubRelease {
    param (
        [string]$Version,
        [string]$Token
    )

    $headers = @{
        "Authorization" = "token $Token"
        "Accept" = "application/vnd.github.v3+json"
    }

    try {
        Invoke-RestMethod -Uri "$gitHubApiUrl/repos/$gitHubOwner/$gitHubRepo/releases/tags/v$Version" -Headers $headers -Method Get -ErrorAction SilentlyContinue | Out-Null
        return $true
    } catch {
        # Release doesn't exist
        return $false
    }
}

# Function to create a GitHub release
function New-GitHubRelease {
    param (
        [string]$Version,
        [string]$ReleaseNotes,
        [bool]$Draft,
        [bool]$Prerelease,
        [string]$Token
    )

    $headers = @{
        "Authorization" = "token $Token"
        "Accept" = "application/vnd.github.v3+json"
    }

    $body = @{
        tag_name = "v$Version"
        name = "v$Version"
        body = $ReleaseNotes
        draft = $Draft
        prerelease = $Prerelease
    } | ConvertTo-Json

    try {
        $releaseResponse = Invoke-RestMethod -Uri "$gitHubApiUrl/repos/$gitHubOwner/$gitHubRepo/releases" -Headers $headers -Method Post -Body $body -ContentType "application/json"
        return $releaseResponse.id
    } catch {
        $errorMessage = $_.Exception.Message
        Write-Host "Error creating GitHub release: $errorMessage" -ForegroundColor Red
        throw
    }
}

# Function to upload assets to a GitHub release
function Add-GitHubReleaseAsset {
    param (
        [int]$ReleaseId,
        [string]$AssetPath,
        [string]$Token
    )

    $headers = @{
        "Authorization" = "token $Token"
        "Accept" = "application/vnd.github.v3+json"
        "Content-Type" = "application/octet-stream"
    }

    $assetName = Split-Path -Path $AssetPath -Leaf
    
    # URL encode the asset name to handle spaces and special characters
    $encodedAssetName = [System.Web.HttpUtility]::UrlEncode($assetName)
    
    # Get the upload URL directly from GitHub to ensure it's correct
    try {
        $releaseInfo = Invoke-RestMethod -Uri "$gitHubApiUrl/repos/$gitHubOwner/$gitHubRepo/releases/$ReleaseId" -Headers @{"Authorization" = "token $Token"; "Accept" = "application/vnd.github.v3+json"} -Method Get
        # Extract the upload URL from the response and format it correctly
        $uploadUrlBase = $releaseInfo.upload_url -replace "\{.*\}", ""
        $uploadUrl = "${uploadUrlBase}?name=$encodedAssetName"
        Write-Host "Using upload URL: $uploadUrl" -ForegroundColor Cyan
    } catch {
        Write-Host "Error getting release info: $($_.Exception.Message)" -ForegroundColor Red
        # Fallback to the standard URL format
        $uploadUrl = "$gitHubApiUrl/repos/$gitHubOwner/$gitHubRepo/releases/$ReleaseId/assets?name=$encodedAssetName"
        Write-Host "Using fallback upload URL: $uploadUrl" -ForegroundColor Yellow
    }
    
    Write-Host "Attempting to upload $assetName to GitHub..." -ForegroundColor Cyan
    
    # Add TLS 1.2 support
    [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
    
    # Set timeout to 5 minutes
    $maxRetries = 3
    $retryCount = 0
    $success = $false
    
    while (-not $success -and $retryCount -lt $maxRetries) {
        try {
            $retryCount++
            if ($retryCount -gt 1) {
                Write-Host "Retry attempt $retryCount of $maxRetries..." -ForegroundColor Yellow
                Start-Sleep -Seconds 5
            }
            
            Invoke-RestMethod -Uri $uploadUrl -Headers $headers -Method Post -InFile $AssetPath -TimeoutSec 300 | Out-Null
            Write-Host "Uploaded $assetName to GitHub release" -ForegroundColor Green
            $success = $true
        } catch {
            $errorMessage = $_.Exception.Message
            Write-Host "Error uploading asset ${assetName}: ${errorMessage}" -ForegroundColor Red
            
            if ($retryCount -ge $maxRetries) {
                Write-Host "Failed to upload after $maxRetries attempts. Continuing with other files..." -ForegroundColor Yellow
            }
        }
    }
}

# Step 1: Clean the output directories
Write-Host "`n[1/6] Cleaning output directories..." -ForegroundColor Yellow
if (Test-Path $serverOutputPath) {
    Remove-Item -Path $serverOutputPath -Recurse -Force
}
New-Item -Path $serverOutputPath -ItemType Directory -Force | Out-Null

# Also clean the dist directory
Push-Location $clientProjectPath
if (Test-Path "dist") {
    Write-Host "Cleaning previous build output..." -ForegroundColor Yellow
    Remove-Item -Path "dist" -Recurse -Force
}
Pop-Location

# Step 2: Build the .NET Server
Write-Host "`n[2/6] Building .NET Server..." -ForegroundColor Yellow
# dotnet publish $serverProjectPath -c Release -o $serverOutputPath --self-contained true -r win-x64 /p:PublishSingleFile=true /p:PublishTrimmed=true
dotnet publish $serverProjectPath -c Release -o $serverOutputPath --self-contained true -r win-x64 /p:PublishSingleFile=true

if (-not $?) {
    Write-Host "Error building .NET Server. Exiting." -ForegroundColor Red
    exit 1
}

Write-Host ".NET Server build completed successfully!" -ForegroundColor Green

# Step 3: Install Node.js dependencies for the Electron app
Write-Host "`n[3/6] Installing Node.js dependencies..." -ForegroundColor Yellow
Push-Location $clientProjectPath
npm install

if (-not $?) {
    Write-Host "Error installing Node.js dependencies. Exiting." -ForegroundColor Red
    Pop-Location
    exit 1
}

# Step 4: Build the Electron app
Write-Host "`n[4/6] Building Electron app..." -ForegroundColor Yellow
npm run build

if (-not $?) {
    Write-Host "Error building Electron app. Exiting." -ForegroundColor Red
    Pop-Location
    exit 1
}

# Step 5: Package the Electron app for Windows
Write-Host "`n[5/6] Packaging Electron app for Windows..." -ForegroundColor Yellow

# If publishing to GitHub, set the GH_TOKEN environment variable
if ($PublishToGitHub) {
    if ([string]::IsNullOrEmpty($GitHubToken)) {
        Write-Host "Error: GitHub token is required for publishing. Use -GitHubToken parameter or set GH_TOKEN environment variable." -ForegroundColor Red
        Pop-Location
        exit 1
    }
    
    # Set the GH_TOKEN environment variable for electron-builder
    $env:GH_TOKEN = $GitHubToken
    Write-Host "GitHub token set for publishing" -ForegroundColor Green
    
    # Run the dist command with publish option
    npm run dist:win
} else {
    # Run the normal dist command without publishing
    npm run dist:win
}

if (-not $?) {
    Write-Host "Error packaging Electron app. Exiting." -ForegroundColor Red
    Pop-Location
    exit 1
}

# Step 6: Generate latest.yml file for auto-updates
Write-Host "`n[6/7] Generating latest.yml file for auto-updates..." -ForegroundColor Yellow

Write-Host "Client project path: $clientProjectPath" -ForegroundColor Cyan

# Check if the client directory exists
if (Test-Path $clientProjectPath) {
    # Change to the client directory
    try {
        Push-Location $clientProjectPath
        Write-Host "Current directory: $(Get-Location)" -ForegroundColor Cyan
        
        # Run the latest.yml generator
        npm run generate-latest-yml
        
        # Return to the original directory
        Pop-Location
    } catch {
        Write-Host "Error running latest.yml generator: $($_.Exception.Message)" -ForegroundColor Red
        # Continue with the build even if the generator fails
        if ((Get-Location).Path -ne $PSScriptRoot) {
            Pop-Location
        }
    }
}

# Step 7: Verify the build output
Write-Host "`n[7/7] Verifying build output..." -ForegroundColor Yellow

# Check if the installer was created
$installerPath = Join-Path -Path (Resolve-Path "dist").Path -ChildPath "*.exe"
$installers = Get-Item $installerPath -ErrorAction SilentlyContinue

if ($installers) {
    Write-Host "Build successful! Found installer(s):" -ForegroundColor Green
    $installers | ForEach-Object {
        Write-Host "  - $($_.Name)" -ForegroundColor Green
    }
} else {
    Write-Host "Warning: No installer found in the dist directory." -ForegroundColor Yellow
}

# Check for the unpacked app
$unpackedPath = Join-Path -Path (Resolve-Path "dist").Path -ChildPath "win-unpacked"
if (Test-Path $unpackedPath) {
    # Verify server files were included properly
    $serverFiles = Join-Path -Path $unpackedPath -ChildPath "server\publish\RecoveryRx.Server.exe"
    if (Test-Path $serverFiles) {
        Write-Host "Server executable found in the correct location." -ForegroundColor Green
    } else {
        Write-Host "Warning: Server executable not found in the expected location." -ForegroundColor Red
        Write-Host "Please check: $serverFiles" -ForegroundColor Yellow
    }
}

Pop-Location

# Step 7: Publish to GitHub (if requested)
if ($PublishToGitHub) {
    Write-Host "`n[7/7] Creating GitHub Release..." -ForegroundColor Yellow
    
    # Check if we're creating a draft release
    if ($Draft) {
        Write-Host "Creating a DRAFT release - you will need to manually publish it from GitHub" -ForegroundColor Cyan
    } else {
        Write-Host "Creating a public release - this will be immediately visible to users" -ForegroundColor Cyan
    }
    
    # Get the current version
    $version = Get-AppVersion
    Write-Host "Publishing version $version to GitHub" -ForegroundColor Cyan
    
    # Check if the release already exists
    if (Test-GitHubRelease -Version $version -Token $GitHubToken) {
        Write-Host "Release v$version already exists on GitHub. Skipping release creation." -ForegroundColor Yellow
    } else {
        # Create a new release
        Write-Host "Creating GitHub release v$version..." -ForegroundColor Cyan
        $releaseId = New-GitHubRelease -Version $version -ReleaseNotes $ReleaseNotes -Draft $Draft -Prerelease $Prerelease -Token $GitHubToken
        
        # Upload the installer and update files
        $distPath = Join-Path -Path $clientProjectPath -ChildPath "dist"
        Write-Host "Looking for installer files in: $distPath" -ForegroundColor Cyan
        
        # List all files in dist directory for debugging
        Write-Host "Files in dist directory:" -ForegroundColor Cyan
        Get-ChildItem -Path $distPath | ForEach-Object { Write-Host "  - $($_.Name)" }
        
        # Create a dist-publish folder for files that need to be uploaded to GitHub
        $publishPath = Join-Path -Path $clientProjectPath -ChildPath "dist-publish"
        if (Test-Path $publishPath) {
            Remove-Item -Path $publishPath -Recurse -Force
        }
        New-Item -Path $publishPath -ItemType Directory -Force | Out-Null
        Write-Host "Created dist-publish folder for GitHub release files: $publishPath" -ForegroundColor Cyan
        
        # Look for any .exe or .msi installer files (not in subdirectories)
        Write-Host "Searching for installer files with full path..." -ForegroundColor Cyan
        $installerFiles = Get-ChildItem -Path $distPath -File | Where-Object { $_.Extension -in ".exe", ".msi" }
        
        # Copy installer files to the publish folder
        foreach ($file in $installerFiles) {
            Write-Host "Found installer: $($file.FullName)" -ForegroundColor Cyan
            $destPath = Join-Path -Path $publishPath -ChildPath $file.Name
            Copy-Item -Path $file.FullName -Destination $destPath -Force
            Write-Host "Copied to publish folder: $destPath" -ForegroundColor Green
        }
        
        $latestYmlPath = Join-Path -Path $distPath -ChildPath "latest.yml"
        # Look for latest.yml in multiple possible locations
        $ymlFound = $false
        if (Test-Path $latestYmlPath) {
            $destYmlPath = Join-Path -Path $publishPath -ChildPath "latest.yml"
            Copy-Item -Path $latestYmlPath -Destination $destYmlPath -Force
            Write-Host "Copied latest.yml to publish folder" -ForegroundColor Green
            $ymlFound = $true
        } else {
            Write-Host "latest.yml not found at expected location: $latestYmlPath" -ForegroundColor Yellow
            Write-Host "Searching for YML files in other locations..." -ForegroundColor Cyan
            
            # Check other potential locations
            $alternateYmlPaths = @(
                (Join-Path -Path $distPath -ChildPath "*.yml"),
                (Join-Path -Path $clientProjectPath -ChildPath "dist\*.yml")
            )
            
            foreach ($pattern in $alternateYmlPaths) {
                $foundYmls = Get-ChildItem -Path $pattern -ErrorAction SilentlyContinue
                if ($foundYmls) {
                    foreach ($yml in $foundYmls) {
                        Write-Host "Found YML file at alternate location: $($yml.FullName)" -ForegroundColor Cyan
                        $destYmlPath = Join-Path -Path $publishPath -ChildPath $yml.Name
                        Copy-Item -Path $yml.FullName -Destination $destYmlPath -Force
                        Write-Host "Copied $($yml.Name) to publish folder" -ForegroundColor Green
                        $ymlFound = $true
                    }
                    break
                }
            }
            
            if (-not $ymlFound) {
                Write-Host "No YML files found. Auto-updates may not work correctly." -ForegroundColor Yellow
                Write-Host "Consider running 'npm run publish:win' to generate the latest.yml file." -ForegroundColor Yellow
            }
        }
        
        if ($installerFiles.Count -gt 0) {
            foreach ($installer in $installerFiles) {
                Write-Host "Uploading installer $($installer.Name) to GitHub release..." -ForegroundColor Cyan
                Add-GitHubReleaseAsset -ReleaseId $releaseId -AssetPath $installer.FullName -Token $GitHubToken
            }
        } else {
            Write-Host "Warning: No installer files found to upload" -ForegroundColor Yellow
        }
        
        # Upload any YML files from the publish folder
        $publishYmlFiles = Get-ChildItem -Path $publishPath -Filter "*.yml" -ErrorAction SilentlyContinue
        if ($publishYmlFiles.Count -gt 0) {
            foreach ($yml in $publishYmlFiles) {
                Write-Host "Uploading $($yml.Name) to GitHub release..." -ForegroundColor Cyan
                Add-GitHubReleaseAsset -ReleaseId $releaseId -AssetPath $yml.FullName -Token $GitHubToken
            }
        } else {
            Write-Host "Warning: No YML files found to upload. Auto-updates may not work correctly." -ForegroundColor Yellow
        }
        
        Write-Host "GitHub release published successfully!" -ForegroundColor Green
    }
}

Write-Host "`n=== Build Complete! ===" -ForegroundColor Cyan
Write-Host "You can find the installer in: $clientProjectPath\dist" -ForegroundColor Green

if ($PublishToGitHub) {
    $version = Get-AppVersion
    Write-Host "GitHub release: https://github.com/$gitHubOwner/$gitHubRepo/releases/tag/v$version" -ForegroundColor Green
    
    if ($Draft) {
        Write-Host "This is a DRAFT release. To publish it:" -ForegroundColor Cyan
        Write-Host "1. Go to the GitHub release page" -ForegroundColor Cyan
        Write-Host "2. Click 'Edit' on the draft release" -ForegroundColor Cyan
        Write-Host "3. Add any additional release notes" -ForegroundColor Cyan
        Write-Host "4. Click 'Publish release' at the bottom of the page" -ForegroundColor Cyan
    }
    
    Write-Host "All files for the release have been copied to: $publishPath" -ForegroundColor Green
}
