using System;

namespace RecoveryRx.Models
{
    public static class UserExtensions
    {
        /// <summary>
        /// Creates a new admin user with the specified password hash
        /// </summary>
        /// <param name="passwordHash">BCrypt hashed password</param>
        /// <returns>A new admin user with default values</returns>
        public static User CreateDefaultAdminUser(string passwordHash)
        {
            return new User
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000000"),
                Username = "admin",
                Email = "<EMAIL>",
                PasswordHash = passwordHash,
                FirstName = "Default",
                LastName = "Admin",
                IsActive = true,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            };
        }
    }
}
