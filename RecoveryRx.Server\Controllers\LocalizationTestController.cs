using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using RecoveryRx.Models;
using RecoveryRx.Server.Helpers;
using RecoveryRx.Server.Resources;
using System.Threading;

namespace RecoveryRx.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class LocalizationTestController : ControllerBase
{
    private readonly IStringLocalizer<SharedResource> _localizer;

    public LocalizationTestController(IStringLocalizer<SharedResource> localizer)
    {
        _localizer = localizer;
    }

    /// <summary>
    /// Tests localization for gender values
    /// </summary>
    /// <returns>Localized gender values based on the Accept-Language header</returns>
    [HttpGet("gender-values")]
    public IActionResult GetGenderValues()
    {
        var result = new
        {
            CurrentCulture = Thread.CurrentThread.CurrentCulture.Name,
            CurrentUICulture = Thread.CurrentThread.CurrentUICulture.Name,
            GenderValues = new
            {
                Male = GenderHelper.GetLocalizedGenderName(Gender.Male),
                Female = GenderHelper.GetLocalizedGenderName(Gender.Female),
                Other = GenderHelper.GetLocalizedGenderName(Gender.Other)
            },
            DirectResourceValues = new
            {
                Male = SharedResource.Gender_Male,
                Female = SharedResource.Gender_Female,
                Other = SharedResource.Gender_Other
            }
        };

        return Ok(result);
    }
}
