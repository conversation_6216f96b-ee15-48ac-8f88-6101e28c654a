
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BinaryBackground from "@/components/BinaryBackground";
import MatrixTrail from "@/components/MatrixTrail";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { Shield, Lock, Eye, UserCheck } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

const Privacy = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [contentRef, contentVisible] = useScrollAnimation(0.1);
  const { t } = useLanguage();

  const infoCollectedItems = t('privacyPage.infoCollected.items') as unknown as string[];
  const infoUseItems = t('privacyPage.infoUse.items') as unknown as string[];
  const yourRightsItems = t('privacyPage.yourRights.items') as unknown as string[];

  // Function to format phone number with LTR enforcement
  const formatPhoneNumber = (phone: string | number) => {
    // Ensure LTR rendering with Unicode LRM (Left-to-Right Mark)
    return <span style={{ direction: 'ltr', display: 'inline-block' }}>{phone}</span>;
  };

  return (
    <div className="min-h-screen bg-background text-foreground relative">
      <BinaryBackground />
      <MatrixTrail />
      <div className="relative z-20">
        <Header />
        
        <section className="py-24">
          <div className="container mx-auto px-6 max-w-4xl">
            <div 
              ref={headerRef}
              className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
            >
              <h1 className="text-5xl font-bold mb-6 text-gradient">
                {t('privacyPage.title')}
              </h1>
              <p className="text-xl text-muted-foreground">
                {t('privacyPage.subtitle')}
              </p>
            </div>

            <div 
              ref={contentRef}
              className={`space-y-8 animate-fade-in-up ${contentVisible ? 'visible' : ''}`}
            >
              <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-6 w-6 text-ai-purple" />
                    {t('privacyPage.dataProtection.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <p>
                    {t('privacyPage.dataProtection.description')}
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-blue/10 to-ai-cyan/10 border-ai-blue/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lock className="h-6 w-6 text-ai-blue" />
                    {t('privacyPage.infoCollected.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <ul className="space-y-2">
                    {infoCollectedItems.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-cyan/10 to-ai-purple/10 border-ai-cyan/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-6 w-6 text-ai-cyan" />
                    {t('privacyPage.infoUse.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <ul className="space-y-2">
                    {infoUseItems.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-violet/10 border-ai-purple/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <UserCheck className="h-6 w-6 text-ai-violet" />
                    {t('privacyPage.yourRights.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <p>You have the right to:</p>
                  <ul className="space-y-2">
                    {yourRightsItems.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-pink/10 to-ai-violet/10 border-ai-pink/30">
                <CardHeader>
                  <CardTitle>{t('privacyPage.contact.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {t('privacyPage.contact.intro')}
                  </p>
                  <div className="mt-4 space-y-2">
                    <p><strong>{t('privacyPage.contact.email')}:</strong> <EMAIL></p>
                    <p><strong>{t('privacyPage.contact.phone')}:</strong> {formatPhoneNumber('+****************')}</p>
                    <p><strong>{t('privacyPage.contact.address')}:</strong> Manila, Philippines</p>
                  </div>
                </CardContent>
              </Card>

              <div className="text-center text-sm text-muted-foreground">
                {t('privacyPage.lastUpdated')}
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default Privacy;
