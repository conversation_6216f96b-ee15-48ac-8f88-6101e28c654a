import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

export const useAuth = () => {
  const [token, setToken] = useState<string | null>(localStorage.getItem('accessToken'));
  const [refreshToken, setRefreshToken] = useState<string | null>(localStorage.getItem('refreshToken'));
  const navigate = useNavigate();

  const login = (accessToken: string, refresh: string) => {
    setToken(accessToken);
    setRefreshToken(refresh);
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refresh);
    axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
  };

  const logout = () => {
    setToken(null);
    setRefreshToken(null);
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    delete axios.defaults.headers.common['Authorization'];
    navigate('/login');
  };

  const refresh = async () => {
    try {
      const response = await axios.post('/api/v1/auth/refresh', { refreshToken });
      login(response.data.accessToken, response.data.refreshToken);
      navigate('/');
    } catch (err) {
      logout();
    }
  };

  return { token, refreshToken, login, logout, refresh };
};