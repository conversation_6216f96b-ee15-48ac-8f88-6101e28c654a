
import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Mail, Phone, MapPin, Send } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BinaryBackground from "@/components/BinaryBackground";
import MatrixTrail from "@/components/MatrixTrail";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";

// Social Media List
const socialMediaList = [
    {link: "https://www.youtube.com/channel/YOUR_CHANNEL_ID", icon: "https://cdn-icons-png.flaticon.com/128/5968/5968852.png", alt: "YouTube"},
    {link: "https://www.linkedin.com/company/maxqit", icon: "https://cdn-icons-png.flaticon.com/128/2504/2504923.png", alt: "LinkedIn"},
    {link: "https://x.com/Maxqit_com", icon: "https://cdn-icons-png.flaticon.com/128/14417/14417709.png", alt: "X (Twitter)"},
    {link: "https://facebook.com/maxqit", icon: "https://cdn-icons-png.flaticon.com/128/2504/2504903.png", alt: "Facebook"},
];

const Contact = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [formRef, formVisible] = useScrollAnimation(0.1);
  const [infoRef, infoVisible] = useScrollAnimation(0.2);
  const { toast } = useToast();
  const { t, isRTL } = useLanguage();

  // Function to format phone number with LTR enforcement
  const formatPhoneNumber = (phone: string | number) => {
    // Ensure LTR rendering with Unicode LRM (Left-to-Right Mark)
    return <span style={{ direction: 'ltr', display: 'inline-block' }}>{phone}</span>;
  };

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    service: "",
    message: ""
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.email || !formData.message) {
      toast({
        title: "Error",
        description: t('contactPage.form.errorRequired'),
        variant: "destructive"
      });
      return;
    }

    // Simulate form submission
    toast({
      title: t('contactPage.form.successTitle'),
      description: t('contactPage.form.successDescription'),
    });

    // Reset form
    setFormData({
      name: "",
      email: "",
      company: "",
      phone: "",
      service: "",
      message: ""
    });
  };

  return (
    <div className="min-h-screen bg-background text-foreground relative">
      <BinaryBackground />
      <MatrixTrail />
      <div className="relative z-20">
        <Header />
        
        <section className="py-24">
          <div className="container mx-auto px-6">
            <div 
              ref={headerRef}
              className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
            >
              <h1 className="text-5xl font-bold mb-6 text-gradient">
                {t('contactPage.title')}
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {t('contactPage.subtitle')}
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div 
                ref={formRef}
                className={`animate-slide-in-left ${formVisible ? 'visible' : ''}`}
              >
                <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30">
                  <CardHeader>
                    <CardTitle className="text-2xl">{t('contactPage.sendMessage')}</CardTitle>
                    <CardDescription>
                      {t('contactPage.formDescription')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">{t('contactPage.form.nameRequired')}</Label>
                          <Input
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            placeholder={t('contactPage.form.namePlaceholder')}
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">{t('contactPage.form.emailRequired')}</Label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            placeholder={t('contactPage.form.emailPlaceholder')}
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="company">{t('contactPage.form.company')}</Label>
                          <Input
                            id="company"
                            name="company"
                            value={formData.company}
                            onChange={handleInputChange}
                            placeholder={t('contactPage.form.companyPlaceholder')}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="phone">{t('contactPage.form.phone')}</Label>
                          <Input
                            id="phone"
                            name="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={handleInputChange}
                            placeholder={t('contactPage.form.phonePlaceholder')}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="service">{t('contactPage.form.service')}</Label>
                        <select
                          id="service"
                          name="service"
                          value={formData.service}
                          onChange={handleInputChange}
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                          <option value="">{t('contactPage.form.selectService')}</option>
                          <option value="ai-web-development">{t('contactPage.form.services.aiWebDevelopment')}</option>
                          <option value="mobile-apps">{t('contactPage.form.services.mobileApps')}</option>
                          <option value="desktop-software">{t('contactPage.form.services.desktopSoftware')}</option>
                          <option value="cross-platform">{t('contactPage.form.services.crossPlatform')}</option>
                          <option value="ai-integration">{t('contactPage.form.services.aiIntegration')}</option>
                          <option value="consulting">{t('contactPage.form.services.consulting')}</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="message">{t('contactPage.form.messageRequired')}</Label>
                        <Textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          placeholder={t('contactPage.form.messagePlaceholder')}
                          className="min-h-[120px]"
                          required
                        />
                      </div>

                      <Button 
                        type="submit"
                        size="lg"
                        className="w-full bg-ai-glow hover:scale-105 transition-all duration-300 animate-glow-pulse"
                      >
                        <Send className="mr-2 h-4 w-4" />
                        {t('contactPage.form.sendButton')}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </div>

              <div 
                ref={infoRef}
                className={`space-y-8 animate-slide-in-right ${infoVisible ? 'visible' : ''}`}
              >
                <Card className="bg-gradient-to-br from-ai-blue/10 to-ai-cyan/10 border-ai-blue/30">
                  <CardHeader>
                    <CardTitle className="text-2xl">{t('contactPage.contactInformation')}</CardTitle>
                    <CardDescription>
                      {t('contactPage.contactDescription')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className={`flex items-center ${isRTL ? 'space-x-reverse' : ''} gap-4`}>
                      <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-ai-cyan to-ai-blue flex items-center justify-center">
                        <Mail className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{t('contactPage.email')}</h3>
                        <p className="text-muted-foreground"><EMAIL></p>
                      </div>
                    </div>

                    <div className={`flex items-center ${isRTL ? 'space-x-reverse' : ''} gap-4`}>
                      <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-ai-violet to-ai-purple flex items-center justify-center">
                        <Phone className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{t('contactPage.phone')}</h3>
                        <p className="text-muted-foreground">{formatPhoneNumber('+****************')}</p>
                      </div>
                    </div>

                    <div className={`flex items-center ${isRTL ? 'space-x-reverse' : ''} gap-4`}>
                      <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-ai-violet to-ai-purple flex items-center justify-center">
                        <Phone className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{t('contactPage.phone')}</h3>
                        <p className="text-muted-foreground">{formatPhoneNumber('+****************')}</p>
                      </div>
                    </div>

                    <div className={`flex items-center ${isRTL ? 'space-x-reverse' : ''} gap-4`}>
                      <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-ai-pink to-ai-violet flex items-center justify-center">
                        <MapPin className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{t('contactPage.office')}</h3>
                        <p className="text-muted-foreground">{t('contactPage.location')}</p>
                      </div>
                    </div>

                    {/* Social Media Section */}
                    <div>
                      <h3 className="font-semibold text-lg mb-4">{t('contactPage.followUs')}</h3>
                      <div className={`flex ${isRTL ? 'space-x-reverse' : ''} gap-4`}>
                        {socialMediaList.map((item, index) => (
                          <a 
                            key={index} 
                            href={item.link} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="transition-transform duration-200 hover:scale-110"
                            aria-label={`Link to our ${item.alt} page`}
                          >
                            <img src={item.icon} alt={item.alt} className="w-10 h-10" />
                          </a>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-violet/10 border-ai-purple/30">
                  <CardHeader>
                    <CardTitle className="text-2xl">{t('contactPage.responseTime')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span>{t('contactPage.responseTimeDescription.email')}</span>
                        <span className="text-ai-cyan font-semibold">{t('contactPage.responseTimeDescription.emailTime')}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>{t('contactPage.responseTimeDescription.consultation')}</span>
                        <span className="text-ai-cyan font-semibold">{t('contactPage.responseTimeDescription.consultationTime')}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>{t('contactPage.responseTimeDescription.emergency')}</span>
                        <span className="text-ai-cyan font-semibold">{t('contactPage.responseTimeDescription.emergencyTime')}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default Contact;
