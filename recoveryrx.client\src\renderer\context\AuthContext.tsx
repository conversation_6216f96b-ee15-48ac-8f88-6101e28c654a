import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';

// Define the shape of the auth state and context
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
}

interface User {
  id: string;
  username: string;
  // Add other user properties as needed, e.g., roles, email
}

interface AuthContextType extends AuthState {
  login: (usernameInput: string, passwordInput: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuthStatus: () => Promise<void>;
}

// Create the context with a default undefined value to ensure it's used within a provider
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Define the props for the AuthProvider
interface AuthProviderProps {
  children: ReactNode;
}

// Create the AuthProvider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true, // Start with loading true to check auth status on mount
  });

  const checkAuthStatus = async () => {
    setAuthState(prev => ({ ...prev, isLoading: true }));
    try {
      // TODO: Replace with actual call to electronBridge to check auth status
      // const currentUser = await window.electronBridge.auth.getCurrentUser();
      // For now, simulate a delay and no user initially
      await new Promise(resolve => setTimeout(resolve, 500));
      const currentUser = null; // Simulate no user initially

      if (currentUser) {
        setAuthState({
          isAuthenticated: true,
          user: currentUser as User, // Cast if your bridge returns a more generic type
          isLoading: false,
        });
      } else {
        setAuthState({
          isAuthenticated: false,
          user: null,
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to check auth status:', error);
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
      });
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const login = async (usernameInput: string, passwordInput: string) => {
    setAuthState(prev => ({ ...prev, isLoading: true }));
    try {
      // TODO: Replace with actual call to electronBridge for login
      // const loggedInUser = await window.electronBridge.auth.login(usernameInput, passwordInput);
      // For now, simulate a successful login
      // The 'passwordInput' parameter will be used when the actual bridge call is implemented.
      await new Promise(resolve => setTimeout(resolve, 1000));
      const loggedInUser: User = { id: '1', username: usernameInput }; // Simulated user

      if (loggedInUser) {
        setAuthState({
          isAuthenticated: true,
          user: loggedInUser,
          isLoading: false,
        });
      } else {
        setAuthState(prev => ({ ...prev, isAuthenticated: false, user: null, isLoading: false }));
        throw new Error('Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setAuthState(prev => ({ ...prev, isAuthenticated: false, user: null, isLoading: false }));
      throw error; // Re-throw to be caught by the calling component
    }
  };

  const logout = async () => {
    setAuthState(prev => ({ ...prev, isLoading: true }));
    try {
      // TODO: Replace with actual call to electronBridge for logout
      // await window.electronBridge.auth.logout();
      // For now, simulate a successful logout
      await new Promise(resolve => setTimeout(resolve, 500));

      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
      });
    } catch (error) {
      console.error('Logout error:', error);
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
      });
    }
  };

  return (
    <AuthContext.Provider value={{ ...authState, login, logout, checkAuthStatus }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
