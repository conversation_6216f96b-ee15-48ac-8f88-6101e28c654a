
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Image, FileText, CheckCircle, Users, Clock } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";
import { Link } from "react-router-dom";

const ProcessSection = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [gridRef, gridVisible] = useScrollAnimation(0.1);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.2);
  const { t } = useLanguage();

  const processSteps = [
    {
      icon: Search,
      title: t('process.discovery.title'),
      description: t('process.discovery.description'),
      color: "text-ai-purple"
    },
    {
      icon: Image,
      title: t('process.design.title'), 
      description: t('process.design.description'),
      color: "text-ai-violet"
    },
    {
      icon: FileText,
      title: t('process.development.title'),
      description: t('process.development.description'),
      color: "text-ai-blue"
    },
    {
      icon: CheckCircle,
      title: t('process.testing.title'),
      description: t('process.testing.description'),
      color: "text-ai-cyan"
    },
    {
      icon: Users,
      title: t('process.launch.title'),
      description: t('process.launch.description'),
      color: "text-ai-pink"
    },
    {
      icon: Clock,
      title: t('process.support.title'),
      description: t('process.support.description'),
      color: "text-ai-purple"
    }
  ];

  return (
    <section id="process" className="py-24">
      <div className="container mx-auto px-6">
        <div 
          ref={headerRef}
          className={`text-center mb-16 animate-slide-in-left ${headerVisible ? 'visible' : ''}`}
        >
          <h2 className="text-5xl font-bold mb-6 text-gradient text-center">
            {t('process.title')}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto text-center">
            {t('process.subtitle')}
          </p>
        </div>
        
        <div 
          ref={gridRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
        >
          {processSteps.map((step, index) => (
            <Card 
              key={index}
              className={`group hover:scale-105 transition-all duration-300 bg-card/50 backdrop-blur-sm border-border hover:border-ai-purple/50 animate-fade-in-up animate-stagger ${gridVisible ? 'visible' : ''}`}
              style={{"--delay": `${index * 0.15}s`} as React.CSSProperties}
            >
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 p-3 rounded-full bg-gradient-to-r from-ai-purple/20 to-ai-blue/20 w-fit group-hover:animate-glow-pulse">
                  <step.icon className={`h-8 w-8 ${step.color}`} />
                </div>
                <CardTitle className="text-xl font-bold group-hover:text-gradient transition-all duration-300">
                  {step.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-muted-foreground">
                  {step.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div 
          ref={ctaRef}
          className={`text-center animate-scale-in ${ctaVisible ? 'visible' : ''}`}
        >
          <Link to="/process">
            <Button 
              size="lg" 
              variant="outline"
              className="border-ai-purple hover:bg-ai-purple/10 px-8 py-4 text-lg"
            >
              {t('process.learnMore')}
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProcessSection;
