import { useAuth } from '@/context/AuthContext'; // Assuming alias @ is configured for renderer path
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import type { ControllerRenderProps } from 'react-hook-form';
import * as z from 'zod';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';
import { AuthLayout } from '../layout/AuthLayout';

// Define the form schema using Zod
const formSchema = z.object({
  username: z.string().min(1, { message: 'Username is required.' }),
  password: z.string().min(1, { message: 'Password is required.' }),
});

type LoginFormValues = z.infer<typeof formSchema>;

export function LoginPage() {
  const { t } = useTranslation();
  const auth = useAuth();
  const [loginError, setLoginError] = useState<string | null>(null);

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const onSubmit = async (values: LoginFormValues) => {
    setLoginError(null);
    try {
      await auth.login(values.username, values.password);
      // Navigation to dashboard will be handled by App.tsx based on isAuthenticated state
    } catch (error) {
      if (error instanceof Error && error.message === 'Login failed') {
        setLoginError(t('auth.loginFailed'));
      } else {
        setLoginError(t('auth.loginError'));
      }
      console.error('Login page error:', error);
    }
  };

  return (
    <AuthLayout>
      <div className="flex items-center justify-center h-full bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            {/* TODO: Replace with your actual logo path or component */}
            <img src="./assets/logo.png" alt="App Logo" className="w-20 h-20 mx-auto mb-4" /> 
            <CardTitle>{t('auth.login')}</CardTitle>
            <CardDescription>{t('auth.enterCredentials')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }: { field: ControllerRenderProps<LoginFormValues, 'username'> }) => (
                    <FormItem>
                      <FormLabel>{t('auth.username')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('auth.usernamePlaceholder')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }: { field: ControllerRenderProps<LoginFormValues, 'password'> }) => (
                    <FormItem>
                      <FormLabel>{t('auth.password')}</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder={t('auth.passwordPlaceholder')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {loginError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>{t('auth.loginFailed')}</AlertTitle>
                    <AlertDescription>{loginError}</AlertDescription>
                  </Alert>
                )}
                <Button type="submit" className="w-full" disabled={auth.isLoading}>
                  {auth.isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('auth.loggingIn')}
                    </>
                  ) : (
                    t('auth.login')
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
          <CardFooter className="flex flex-col items-center text-xs text-muted-foreground">
            <p>&copy; {new Date().getFullYear()} RecoveryRx. All rights reserved.</p>
          </CardFooter>
        </Card>
      </div>
    </AuthLayout>
  );
}
