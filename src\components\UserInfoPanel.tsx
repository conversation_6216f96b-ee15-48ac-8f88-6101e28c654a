
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MessageCircle, Phone, X } from 'lucide-react';

interface UserInfoPanelProps {
  onStartChat: (userInfo: UserInfo) => void;
  onStartCall: (userInfo: UserInfo) => void;
  onClose: () => void;
}

export interface UserInfo {
  name: string;
  email: string;
  phone?: string;
}

const UserInfoPanel = ({ onStartChat, onStartCall, onClose }: UserInfoPanelProps) => {
  const [userInfo, setUserInfo] = useState<UserInfo>({
    name: '',
    email: '',
    phone: ''
  });

  const isFormValid = userInfo.name.trim() && userInfo.email.trim();

  const handleStartChat = () => {
    if (isFormValid) {
      onStartChat(userInfo);
    }
  };

  const handleStartCall = () => {
    if (isFormValid) {
      onStartCall(userInfo);
    }
  };

  return (
    <div className="fixed bottom-24 right-6 w-80 bg-background border border-border rounded-lg shadow-xl z-50 animate-slide-in-up">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-gradient">Connect with Maxqit</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 hover:bg-destructive/20"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form Fields */}
        <div className="space-y-4 mb-6">
          <div>
            <Input
              placeholder="Your Name"
              value={userInfo.name}
              onChange={(e) => setUserInfo(prev => ({ ...prev, name: e.target.value }))}
              className="bg-card border-ai-purple/30 focus:border-ai-cyan"
            />
          </div>
          <div>
            <Input
              type="email"
              placeholder="Your Email"
              value={userInfo.email}
              onChange={(e) => setUserInfo(prev => ({ ...prev, email: e.target.value }))}
              className="bg-card border-ai-purple/30 focus:border-ai-cyan"
            />
          </div>
          <div>
            <Input
              type="tel"
              placeholder="Your Phone (Optional)"
              value={userInfo.phone}
              onChange={(e) => setUserInfo(prev => ({ ...prev, phone: e.target.value }))}
              className="bg-card border-ai-purple/30 focus:border-ai-cyan"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3">
          <Button
            onClick={handleStartChat}
            disabled={!isFormValid}
            className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Start Chat
          </Button>
          <Button
            onClick={handleStartCall}
            disabled={!isFormValid}
            className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            <Phone className="w-4 h-4 mr-2" />
            Live Call
          </Button>
        </div>

        {/* AI-inspired decorations */}
        <div className="absolute top-2 left-2 w-2 h-2 bg-ai-cyan rounded-full opacity-50 animate-pulse"></div>
        <div className="absolute bottom-2 right-2 w-1 h-1 bg-ai-purple rounded-full opacity-60"></div>
      </div>
    </div>
  );
};

export default UserInfoPanel;
