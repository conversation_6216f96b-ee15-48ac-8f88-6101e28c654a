using RecoveryRx.Server.DTOs.User;
using RecoveryRx.Server.DTOs.UserRole;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Services
{
    public interface IUserService
    {
        Task<UserDto?> GetUserByIdAsync(Guid id);
        Task<UserDto?> GetUserByUsernameAsync(string username);
        Task<IEnumerable<UserDto>> GetAllUsersAsync();
        Task<(UserDto? User, string? ErrorMessage)> CreateUserAsync(CreateUserDto createUserDto);
        Task<(bool Success, string? ErrorMessage)> UpdateUserAsync(Guid id, UpdateUserDto updateUserDto);
        Task<bool> DeleteUserAsync(Guid id);
        Task<(bool Success, string? ErrorMessage)> AssignRoleToUserAsync(AssignRoleToUserDto assignRoleDto);
        Task<(bool Success, string? ErrorMessage)> RemoveRoleFromUserAsync(AssignRoleToUserDto removeRoleDto); // Reusing DTO for consistency
    }
}
