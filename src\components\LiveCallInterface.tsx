
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Mic, MicOff, Phone, PhoneOff, Volume2, X } from 'lucide-react';
import { UserInfo } from './UserInfoPanel';

interface LiveCallInterfaceProps {
  userInfo: UserInfo;
  onClose: () => void;
}

const LiveCallInterface = ({ userInfo, onClose }: LiveCallInterfaceProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(80);
  const [callDuration, setCallDuration] = useState(0);
  const [audioLevels, setAudioLevels] = useState<number[]>([]);

  useEffect(() => {
    // Simulate connection process
    const connectTimer = setTimeout(() => {
      setIsConnected(true);
    }, 3000);

    return () => clearTimeout(connectTimer);
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isConnected) {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isConnected]);

  useEffect(() => {
    // Simulate audio waveform
    const waveInterval = setInterval(() => {
      setAudioLevels(prev => [
        ...prev.slice(-19),
        Math.random() * 100
      ]);
    }, 100);

    return () => clearInterval(waveInterval);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const endCall = () => {
    onClose();
  };

  return (
    <div className="fixed bottom-6 right-6 w-80 bg-gradient-to-b from-gray-900 to-black border border-ai-cyan/30 rounded-lg shadow-xl z-50 animate-slide-in-up">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-bold text-white">Live Call with Maxqit</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 hover:bg-white/10 text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Status and Timer */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-400' : 'bg-yellow-400'} animate-pulse`}></div>
            <span className="text-white font-medium">
              {isConnected ? 'Connected' : 'Connecting...'}
            </span>
          </div>
          {isConnected && (
            <div className="text-ai-cyan text-lg font-mono">
              {formatTime(callDuration)}
            </div>
          )}
        </div>

        {/* Audio Waveform */}
        <div className="flex items-center justify-center h-16 mb-6 bg-black/50 rounded-lg p-4">
          <div className="flex items-end space-x-1 h-8">
            {audioLevels.map((level, index) => (
              <div
                key={index}
                className="w-1 bg-gradient-to-t from-ai-cyan to-ai-purple rounded-full transition-all duration-100"
                style={{ height: `${Math.max(level / 4, 4)}px` }}
              ></div>
            ))}
          </div>
        </div>

        {/* Controls */}
        <div className="space-y-4">
          {/* Mute Button */}
          <div className="flex items-center justify-center">
            <Button
              onClick={() => setIsMuted(!isMuted)}
              className={`w-12 h-12 rounded-full ${
                isMuted 
                  ? 'bg-red-500 hover:bg-red-600' 
                  : 'bg-ai-cyan hover:bg-ai-cyan/80'
              } transition-all duration-300`}
            >
              {isMuted ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
            </Button>
          </div>

          {/* Volume Control */}
          <div className="flex items-center space-x-3">
            <Volume2 className="w-4 h-4 text-ai-cyan" />
            <div className="flex-1 relative">
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => setVolume(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                style={{
                  background: `linear-gradient(to right, #00D4FF 0%, #00D4FF ${volume}%, #374151 ${volume}%, #374151 100%)`
                }}
              />
            </div>
            <span className="text-xs text-ai-cyan w-8">{volume}%</span>
          </div>

          {/* End Call Button */}
          <div className="flex justify-center pt-2">
            <Button
              onClick={endCall}
              className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105"
            >
              <PhoneOff className="w-4 h-4 mr-2" />
              End Call
            </Button>
          </div>
        </div>

        {/* AI decorative elements */}
        <div className="absolute top-2 left-2 w-2 h-2 bg-ai-cyan rounded-full opacity-50 animate-ping"></div>
        <div className="absolute bottom-2 right-2 w-1 h-1 bg-ai-purple rounded-full opacity-60 animate-pulse"></div>
        <div className="absolute top-1/2 left-1 w-1 h-1 bg-ai-cyan rounded-full opacity-40"></div>
      </div>
    </div>
  );
};

export default LiveCallInterface;
