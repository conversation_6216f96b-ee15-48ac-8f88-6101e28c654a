{"name": "recoveryrx.client", "private": true, "version": "0.0.3", "description": "RecoveryRx Client", "type": "module", "author": "RecoveryRx", "homepage": "https://recoveryrx.keleshteri.com", "main": "dist-electron/main.js", "scripts": {"dev": "npm-run-all --parallel dev:server dev:react  dev:electron", "dev:react": "vite", "dev:electron": "npm run transpile:electron &&  cross-env NODE_ENV=development electron .", "dev:server": " cd ../RecoveryRx.Server && dotnet run", "build": "tsc && vite build && node copy-splash.js", "preview": "vite preview", "transpile:electron": "tsc --project tsconfig.electron.json", "dist:mac": "npm run transpile:electron && npm run build && electron-builder --mac --arm64", "dist:win": "npm run transpile:electron && npm run build && electron-builder --win --x64", "dist:linux": "npm run transpile:electron && npm run build && electron-builder --linux --x64", "publish:win": "npm run transpile:electron && npm run build && cross-env-shell \"electron-builder --win --x64 --publish always\"", "generate-latest-yml": "node --experimental-modules generate-latest-yml.js", "test:e2e": "playwright test", "test:unit": "vitest src"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/vite": "^4.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-updater": "^6.6.2", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.511.0", "os-utils": "^0.0.14", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "zod": "^3.25.28"}, "devDependencies": {"@eslint/js": "^9.25.0", "@playwright/test": "^1.52.0", "@types/node": "^20.17.47", "@types/os-utils": "^0.0.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "cross-env": "^7.0.3", "electron": "^36.2.1", "electron-builder": "^26.0.12", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "npm-run-all": "^4.1.5", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.1.3", "wait-on": "^8.0.3"}}