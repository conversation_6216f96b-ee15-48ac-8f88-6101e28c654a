using System.ComponentModel.DataAnnotations;
using RecoveryRx.Models;

namespace RecoveryRx.Server.DTOs;

public class CreateUpdatePatientDto
{
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;

    [MaxLength(50)]
    public string? NationalIdNumber { get; set; }

    [Required]
    public Gender Gender { get; set; }

    [Required]
    public DateTime DateOfBirth { get; set; }

    [MaxLength(20)]
    [Phone]
    public string? MobileNumber { get; set; }

    [MaxLength(20)]
    [Phone]
    public string? LandlineNumber { get; set; }

    [MaxLength(500)]
    public string? Address { get; set; }

    public string? ImagePath { get; set; }
}
