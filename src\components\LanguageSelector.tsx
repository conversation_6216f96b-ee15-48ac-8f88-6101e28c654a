
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Globe } from 'lucide-react';
import { useLanguage, Language } from '@/contexts/LanguageContext';

const languages = {
  en: { name: 'English', flag: 'EN' },
  ar: { name: 'العربية', flag: 'AR' },
  fr: { name: 'Français', flag: 'FR' },
  es: { name: 'Español', flag: 'ES' },
  fa: { name: 'فارسی', flag: 'FA' },
};

const LanguageSelector = () => {
  const { language, setLanguage, isRTL } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="border-ai-purple/30 hover:bg-ai-purple/10 flex items-center gap-2"
      >
        <Globe className="h-4 w-4" />
        {languages[language].flag}
      </Button>
      
      {isOpen && (
        <div className={`absolute top-full mt-2 ${isRTL ? 'left-0' : 'right-0'} bg-background border border-border rounded-lg shadow-lg z-50 min-w-[160px]`}>
          {Object.entries(languages).map(([code, lang]) => (
            <button
              key={code}
              onClick={() => {
                setLanguage(code as Language);
                setIsOpen(false);
              }}
              className={`w-full px-4 py-2 text-center hover:bg-accent flex items-center justify-center gap-2 first:rounded-t-lg last:rounded-b-lg ${
                language === code ? 'bg-accent' : ''
              }`}
            >
              <span>{lang.flag}</span>
              <span>{lang.name}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
