---
trigger: model_decision
description: "Rules for implementing and using the Electron Bridge pattern in RecoveryRx"
---

# Electron Bridge Pattern Rules

## Core Principles

1. **Secure Communication**:
   - All communication between renderer and main processes MUST use IPC
   - Never modify Content Security Policy to allow direct API access from renderer
   - All API calls MUST go through the main process

2. **Type Safety**:
   - Use TypeScript interfaces for all IPC messages
   - Maintain consistent types across process boundaries
   - Document message formats clearly

## Implementation Structure

1. **Bridge Organization**:
   - Keep all bridge-related code in `src/renderer/utils/electronBridge.ts` for the renderer side
   - Implement corresponding handlers in `src/electron/ipc/handlers.ts` for the main process
   - Define shared types in a common location accessible to both

2. **Channel Naming**:
   - Use consistent naming convention for IPC channels
   - Prefix channels with domain/feature name
   - Use verb-noun format for action channels (e.g., `user:authenticate`, `data:fetch`)

3. **Message Format**:
   - Include request ID in messages for correlation
   - Use consistent structure for request and response messages
   - Include proper error information in failed responses

## Usage Patterns

1. **In React Components**:
   - Import the bridge from `src/renderer/utils/electronBridge.ts`
   - Use the provided methods for all IPC communication
   - Handle loading states and errors consistently

2. **In Main Process**:
   - Register handlers for all IPC channels
   - Implement proper error handling for API calls
   - Return structured responses to the renderer

3. **Error Handling**:
   - Implement consistent error handling across the bridge
   - Provide meaningful error messages to users
   - Log errors appropriately for debugging

## API Communication

1. **Request Pattern**:
   - Make API requests from the main process
   - Use typed request and response interfaces
   - Handle network errors properly

2. **Response Handling**:
   - Process API responses in the main process
   - Transform data if necessary before sending to renderer
   - Include metadata for pagination, errors, etc.

## Security Considerations

1. **Input Validation**:
   - Validate all data received from renderer before processing
   - Sanitize data before making API calls
   - Never trust user input without validation

2. **Error Exposure**:
   - Be careful about exposing sensitive error details to the renderer
   - Sanitize error messages when appropriate
   - Log detailed errors for debugging but send sanitized versions to UI

## Performance Optimization

1. **Message Size**:
   - Be mindful of the size of data sent through IPC
   - Consider pagination or chunking for large datasets
   - Optimize serialization/deserialization of messages

2. **Caching Strategy**:
   - Implement caching where appropriate
   - Consider which process should own cached data
   - Implement proper cache invalidation

## Testing

1. **Mock Implementation**:
   - Create mock implementations of the bridge for testing React components
   - Test IPC handlers in isolation
   - Implement integration tests for critical paths

2. **Error Scenarios**:
   - Test error handling paths
   - Simulate network failures
   - Verify proper error messages are displayed to users