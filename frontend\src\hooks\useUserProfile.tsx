import { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

interface UserProfile {
  id: number;
  email: string;
  username: string;
  profile: any;
  createdAt: string;
  isActive: boolean;
  role: string;
  twoFactorEnabled: boolean;
  emailVerified: boolean;
  tenantId: string;
  status: string;
  skills: any;
}

export const useUserProfile = () => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, token } = useAuth();

  const fetchUserProfile = async () => {
    if (!isAuthenticated || !token) {
      setUser(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await axios.get('/api/v1/user/me');
      setUser(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch user profile');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserProfile();
  }, [isAuthenticated, token]);

  return {
    user,
    loading,
    error,
    refetch: fetchUserProfile
  };
};
