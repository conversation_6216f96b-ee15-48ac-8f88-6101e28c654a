using Microsoft.AspNetCore.Mvc;
using RecoveryRx.Server.Services;
using System.Threading.Tasks;
using System;
using RecoveryRx.Server.DTOs.Database.Requests;
using RecoveryRx.Server.DTOs.Database.Responses;

namespace RecoveryRx.Server.Controllers;

/// <summary>
/// API controller for managing database operations such as testing connections, 
/// retrieving provider information, initializing the database, and applying migrations.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DatabaseController : ControllerBase
{
    private readonly IDatabaseService _databaseService;

    /// <summary>
    /// Initializes a new instance of the <see cref="DatabaseController"/> class.
    /// </summary>
    /// <param name="databaseService">The database service injected by DI.</param>
    public DatabaseController(IDatabaseService databaseService)
    {
        _databaseService = databaseService;
    }

    /// <summary>
    /// Tests the current database connection and returns status, provider, and connection details.
    /// </summary>
    /// <remarks>
    /// This endpoint attempts to connect to the database configured for the application.
    /// It provides information about the connection status, the database provider being used (e.g., SQLite, MySQL),
    /// and sanitized connection details (e.g., database file path or server/database name, omitting sensitive information).
    /// </remarks>
    /// <returns>An <see cref="IActionResult"/> containing a <see cref="TestConnectionResponse"/> with connection details.</returns>
    /// <response code="200">Returns the database connection status, provider, and sanitized connection details.</response>
    /// <response code="500">If an unexpected error occurs during the connection test.</response>
    [HttpGet("test-connection")]
    [ProducesResponseType(typeof(TestConnectionResponse), 200)]
    [ProducesResponseType(typeof(object), 500)] // For unexpected errors
    public async Task<IActionResult> TestConnection()
    {
        try
        {
            var response = await _databaseService.TestConnectionAsync();
            return Ok(response);
        }
        catch (Exception ex)
        {
            // Log the exception (not shown here for brevity)
            return StatusCode(500, new { Error = $"An unexpected error occurred while testing connection: {ex.Message}" });
        }
    }

    /// <summary>
    /// Gets the currently configured database provider (e.g., SQLite, MySQL).
    /// </summary>
    /// <remarks>
    /// The provider is determined based on application configuration (e.g., command-line arguments, appsettings.json).
    /// </remarks>
    /// <returns>An <see cref="IActionResult"/> containing a <see cref="ProviderResponse"/> with the provider name.</returns>
    /// <response code="200">Returns the name of the current database provider.</response>
    /// <response code="500">If an unexpected error occurs while retrieving the provider.</response>
    [HttpGet("provider")]
    [ProducesResponseType(typeof(ProviderResponse), 200)]
    [ProducesResponseType(typeof(object), 500)]
    public async Task<IActionResult> GetProvider()
    {
        try
        {
            var response = await _databaseService.GetDatabaseProviderAsync();
            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { Error = $"An unexpected error occurred while retrieving provider: {ex.Message}" });
        }
    }

    /// <summary>
    /// Initializes the database. This typically involves ensuring the database schema is created.
    /// </summary>
    /// <remarks>
    /// This endpoint attempts to ensure the database is created according to the current model configuration.
    /// If using Entity Framework Core, this might involve `EnsureCreatedAsync`. 
    /// It does NOT typically run migrations; use the `apply-migrations` endpoint for that.
    /// The response includes the provider, database details, and actions performed.
    /// </remarks>
    /// <returns>An <see cref="IActionResult"/> containing an <see cref="InitializeDatabaseResponse"/> with the initialization outcome.</returns>
    /// <response code="200">Returns details of the database initialization process.</response>
    /// <response code="500">If an unexpected error occurs during initialization.</response>
    [HttpPost("initialize")]
    [ProducesResponseType(typeof(InitializeDatabaseResponse), 200)]
    [ProducesResponseType(typeof(object), 500)]
    public async Task<IActionResult> Initialize()
    {
        try
        {
            var response = await _databaseService.InitializeDatabaseAsync();
            // Depending on the outcome in response.Message or specific flags, you might return different status codes
            // For simplicity, we'll return Ok and let the client inspect the response DTO.
            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { Error = $"An unexpected error occurred during database initialization: {ex.Message}" });
        }
    }

    /// <summary>
    /// Applies any pending Entity Framework Core migrations to the database for the current provider.
    /// </summary>
    /// <remarks>
    /// This endpoint will execute `DbContext.Database.MigrateAsync()` or equivalent.
    /// The response details which migrations were applied (if any) or if no migrations were pending.
    /// </remarks>
    /// <returns>An <see cref="IActionResult"/> containing an <see cref="ApplyMigrationsResponse"/> with the migration outcome.</returns>
    /// <response code="200">Returns details of the migration application process.</response>
    /// <response code="500">If an unexpected error occurs while applying migrations.</response>
    [HttpPost("apply-migrations")]
    [ProducesResponseType(typeof(ApplyMigrationsResponse), 200)]
    [ProducesResponseType(typeof(object), 500)]
    public async Task<IActionResult> ApplyMigrations()
    {
        try
        {
            var response = await _databaseService.ApplyMigrationsAsync();
            return Ok(response);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { Error = $"An unexpected error occurred while applying migrations: {ex.Message}" });
        }
    }

    /// <summary>
    /// Configures the database provider and connection settings in `appsettings.json`.
    /// </summary>
    /// <remarks>
    /// This endpoint allows runtime modification of the `DatabaseSettings:Provider` and corresponding connection string
    /// in `appsettings.json`. 
    /// **An application restart is typically required for these changes to take full effect.**
    /// </remarks>
    /// <param name="settings">The database configuration settings (<see cref="UpdateDatabaseSettingsRequest"/>).</param>
    /// <returns>An <see cref="IActionResult"/> indicating the result of the configuration update.</returns>
    /// <response code="200">Returns a success message if the settings are updated successfully. Restart may be needed.</response>
    /// <response code="400">If the input settings are invalid or the update fails for a known reason (e.g., provider not supported for dynamic update).</response>
    /// <response code="500">Returns an error message if an internal server error occurs during the update.</response>
    [HttpPost("configure-provider")]
    [ProducesResponseType(typeof(object), 200)] // Keeping generic object for success message
    [ProducesResponseType(typeof(object), 400)] // For ModelState errors or known update failures
    [ProducesResponseType(typeof(object), 500)]
    public async Task<IActionResult> ConfigureProvider([FromBody] UpdateDatabaseSettingsRequest settings)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var success = await _databaseService.UpdateDatabaseSettingsAsync(settings);
            if (success)
            {
                return Ok(new { Message = "Database provider settings updated successfully. An application restart is typically required for changes to take full effect." });
            }
            else
            {
                // The service now logs specifics, so a general message here is okay.
                return BadRequest(new { Error = "Failed to update database provider settings. This could be due to missing configuration for the selected provider or an internal issue. Check server logs for details." });
            }
        }
        catch (Exception ex)
        {
            // Log the exception details (not done here for brevity, but important in production)
            return StatusCode(500, new { Error = $"An unexpected error occurred while configuring provider: {ex.Message}" });
        }
    }
}
