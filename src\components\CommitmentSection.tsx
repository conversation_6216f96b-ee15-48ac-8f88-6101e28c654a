
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";
import { Link } from "react-router-dom";

const CommitmentSection = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [cardsRef, cardsVisible] = useScrollAnimation(0.1);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.2);
  const { t, isRTL } = useLanguage();

  return (
    <section className="py-24 bg-gradient-to-br from-ai-purple/5 via-transparent to-ai-blue/5">
      <div className="container mx-auto px-6">
        <div 
          ref={headerRef}
          className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
        >
          <h2 className="text-5xl font-bold mb-6 text-gradient text-center">
            {t('commitment.title')}
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed text-center">
            {t('commitment.subtitle')}
          </p>
        </div>
        
        <div 
          ref={cardsRef}
          className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12"
        >
          <Card className={`bg-gradient-to-br from-ai-purple/10 to-ai-violet/10 border-ai-purple/30 hover:glow-box transition-all duration-300 text-center animate-slide-in-left ${cardsVisible ? 'visible' : ''}`}>
            <CardHeader>
              <CardTitle className="text-4xl font-bold text-ai-cyan text-center">{t('commitment.satisfaction.rate')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-lg text-center">
                {t('commitment.satisfaction.description')}
              </CardDescription>
            </CardContent>
          </Card>
          
          <Card className={`bg-gradient-to-br from-ai-blue/10 to-ai-cyan/10 border-ai-blue/30 hover:glow-box transition-all duration-300 text-center animate-slide-in-right ${cardsVisible ? 'visible' : ''}`}
                style={{"--delay": "0.2s"} as React.CSSProperties}>
            <CardHeader>
              <CardTitle className="text-4xl font-bold text-ai-pink text-center">{t('commitment.projects.count')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-lg text-center">
                {t('commitment.projects.description')}
              </CardDescription>
            </CardContent>
          </Card>
        </div>
        
        <div 
          ref={ctaRef}
          className={`text-center animate-scale-in ${ctaVisible ? 'visible' : ''}`}
        >
          <Link to="/contact">
            <Button 
              size="lg" 
              className="bg-ai-glow hover:scale-105 transition-all duration-300 px-8 py-4 text-lg animate-glow-pulse"
            >
              {t('commitment.cta')}
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CommitmentSection;
