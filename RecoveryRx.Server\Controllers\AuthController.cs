using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RecoveryRx.Server.DTOs.Auth;
using RecoveryRx.Server.Services.Auth;
using System;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILoginActivityService _loginActivityService;

        public AuthController(
            IAuthService authService,
            ILoginActivityService loginActivityService)
        {
            _authService = authService;
            _loginActivityService = loginActivityService;
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequestDto registerDto)
        {
            try
            {
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
                var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
                
                var result = await _authService.RegisterAsync(registerDto, ipAddress, userAgent);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred during registration", error = ex.Message });
            }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequestDto loginDto)
        {
            try
            {
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
                var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
                
                var result = await _authService.LoginAsync(loginDto, ipAddress, userAgent);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred during login", error = ex.Message });
            }
        }

        [Authorize]
        [HttpGet("me")]
        public async Task<IActionResult> GetCurrentUser()
        {
            try
            {
                var userId = Guid.Parse(User.FindFirst("sub")?.Value ?? string.Empty);
                var result = await _authService.GetCurrentUserAsync(userId);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving user information", error = ex.Message });
            }
        }

        [Authorize(Roles = "Admin")]
        [HttpGet("login-activity")]
        public async Task<IActionResult> GetRecentLoginActivity([FromQuery] int limit = 50)
        {
            try
            {
                var result = await _loginActivityService.GetRecentLoginAttemptsAsync(limit);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving login activity", error = ex.Message });
            }
        }

        [Authorize]
        [HttpGet("my-login-history")]
        public async Task<IActionResult> GetMyLoginHistory([FromQuery] int limit = 10)
        {
            try
            {
                var userId = Guid.Parse(User.FindFirst("sub")?.Value ?? string.Empty);
                var result = await _loginActivityService.GetLoginHistoryForUserAsync(userId, limit);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving login history", error = ex.Message });
            }
        }

        [Authorize(Roles = "Admin")]
        [HttpPost("cleanup-login-records")]
        public async Task<IActionResult> CleanupLoginRecords([FromQuery] int daysToKeep = 90)
        {
            try
            {
                await _loginActivityService.CleanupOldLoginRecordsAsync(daysToKeep);
                return Ok(new { message = $"Successfully cleaned up login records older than {daysToKeep} days" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while cleaning up login records", error = ex.Message });
            }
        }
    }
}
