using Microsoft.EntityFrameworkCore;
using System;

namespace RecoveryRx.Models;

public class RecoveryRxContext : DbContext
{
    public RecoveryRxContext(DbContextOptions<RecoveryRxContext> options) : base(options)
    {
    }
    // Add your DbSets here
    public DbSet<User> Users { get; set; } = null!;
    public DbSet<Role> Roles { get; set; } = null!;
    public DbSet<UserRole> UserRoles { get; set; } = null!;
    public DbSet<Patient> Patients { get; set; } = null!;
    public DbSet<LoginActivity> LoginActivities { get; set; } = null!;


    public static async Task InitializeAsync(RecoveryRxContext db)
    {
        await db.Database.MigrateAsync();
        
        // Check if admin user exists and has a placeholder password
        var adminUser = await db.Users.FindAsync(Guid.Parse("10000000-0000-0000-0000-000000000000"));
        if (adminUser != null && adminUser.PasswordHash == "TO_BE_REPLACED_WITH_ACTUAL_HASH")
        {
            // Set a default password for development environments only
            // In production, this should be changed immediately
            var defaultPassword = "Admin@123456";
            adminUser.PasswordHash = BCrypt.Net.BCrypt.HashPassword(defaultPassword, BCrypt.Net.BCrypt.GenerateSalt(12));
            await db.SaveChangesAsync();
            
            Console.WriteLine("\n===== SECURITY WARNING =====\n");
            Console.WriteLine("Default admin password has been set to: " + defaultPassword);
            Console.WriteLine("Please change this password immediately in production environments!");
            Console.WriteLine("\n===========================\n");
        }
    }

    /// <summary>
    /// Configures the model that was discovered by convention from the entity types
    /// exposed in <see cref="DbSet{TEntity}"/> properties on your derived context.
    /// The resulting model may be cached and re-used for subsequent instances of your derived context.
    /// </summary>
    /// <param name="modelBuilder">The builder being used to construct the model for this context.
    /// Databases (and other extensions) typically define extension methods on this object that allow you to configure aspects of the model that are specific
    /// to a given database.</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Configure Guid columns for MySQL compatibility
        if (Database.ProviderName == "Pomelo.EntityFrameworkCore.MySql")
        {
            modelBuilder.Entity<User>()
                .Property(u => u.Id)
                .HasColumnType("char(36)")
                .ValueGeneratedOnAdd();
                
            modelBuilder.Entity<Role>()
                .Property(r => r.Id)
                .HasColumnType("char(36)")
                .ValueGeneratedOnAdd();
        }
        
        // Configure LoginActivity entity
        modelBuilder.Entity<LoginActivity>(entity =>
        {
            entity.HasOne(la => la.User)
                .WithMany()
                .HasForeignKey(la => la.UserId)
                .IsRequired();
        });

        // Configure your entities here
        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(ur => new { ur.UserId, ur.RoleId });

            entity.HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserId)
                .IsRequired();

            entity.HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleId)
                .IsRequired();
        });

        // Seed Roles
        var adminRoleId = Guid.Parse("00000001-0000-0000-0000-000000000000");
        var doctorRoleId = Guid.Parse("00000002-0000-0000-0000-000000000000");
        var managerRoleId = Guid.Parse("00000003-0000-0000-0000-000000000000");
        var cashierRoleId = Guid.Parse("00000004-0000-0000-0000-000000000000");

        modelBuilder.Entity<Role>().HasData(
            new Role { Id = adminRoleId, Name = "Admin", Description = "Administrator with full access" },
            new Role { Id = doctorRoleId, Name = "Doctor", Description = "Medical doctor" },
            new Role { Id = managerRoleId, Name = "Manager", Description = "Clinic manager" },
            new Role { Id = cashierRoleId, Name = "Cashier", Description = "Handles payments and billing" }
        );

        // Seed Default Admin User
        var adminUserId = Guid.Parse("10000000-0000-0000-0000-000000000000"); 

        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = adminUserId,
                Username = "admin",
                Email = "<EMAIL>",
                PasswordHash = "TO_BE_REPLACED_WITH_ACTUAL_HASH", 
                FirstName = "Default",
                LastName = "Admin",
                IsActive = true,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc) 
            }
        );

        // Seed UserRole (Link Admin User to Admin Role)
        modelBuilder.Entity<UserRole>().HasData(
            new UserRole { UserId = adminUserId, RoleId = adminRoleId }
        );
    }
}
