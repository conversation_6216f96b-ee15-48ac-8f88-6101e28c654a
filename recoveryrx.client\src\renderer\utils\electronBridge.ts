// This file provides a bridge between Electron and web environments
// It detects whether we're running in Electron or a web browser and provides appropriate implementations

// Define the types for our Statistics and other data structures
export interface Statistics {
  cpuUsage: number;
  ramUsage: number;
  storageUsage: number;
  timestamp: number;
}

export interface StaticData {
  cpuModel: string;
  totalMemoryGB: number;
  totalStorage: number;
}

export type View = 'CPU' | 'RAM' | 'STORAGE';
export type FrameAction = 'MINIMIZE' | 'MAXIMIZE' | 'UNMAXIMIZE' | 'CLOSE' | 'MINIMIZE_TO_TRAY';

export interface ElectronBridge {
  subscribeStatistics: (callback: (stats: Statistics) => void) => () => void;
  subscribeChangeView: (callback: (view: View) => void) => () => void;
  sendFrameAction: (action: FrameAction) => void;
  getStaticData: () => Promise<StaticData>;
}

// Mock data for web environment
const mockStaticData: StaticData = {
  cpuModel: 'Web Browser CPU',
  totalMemoryGB: 16,
  totalStorage: 512
};

// Create random statistics for web environment
const createMockStatistics = (): Statistics => ({
  cpuUsage: Math.random() * 100,
  ramUsage: Math.random() * 100,
  storageUsage: Math.random() * 100,
  timestamp: Date.now()
});

// Implementation for web environment
const webImplementation: ElectronBridge = {
  subscribeStatistics: (callback) => {
    // Simulate statistics updates every second
    const interval = setInterval(() => {
      callback(createMockStatistics());
    }, 1000);
    
    return () => clearInterval(interval);
  },
  
  subscribeChangeView: (_callback) => {
    // No-op for web environment
    return () => {};
  },
  
  sendFrameAction: (action) => {
    // No-op for web environment
    console.log(`Frame action ${action} not applicable in web environment`);
  },
  
  getStaticData: async () => {
    return mockStaticData;
  }
};

// Function to get the electron bridge
function getElectronBridge(): ElectronBridge {
  // Check if we're in an Electron environment
  if (typeof window !== 'undefined' && 'electron' in window) {
    // We're in Electron, use the native implementation
    const nativeElectron = window.electron as any;
    
    // Make sure all required methods exist
    if (nativeElectron && 
        typeof nativeElectron.subscribeStatistics === 'function' &&
        typeof nativeElectron.subscribeChangeView === 'function' &&
        typeof nativeElectron.sendFrameAction === 'function' &&
        typeof nativeElectron.getStaticData === 'function') {
      return nativeElectron as ElectronBridge;
    }
  }
  
  // Either we're in a web browser or the Electron API is incomplete
  console.log('Using web implementation for Electron bridge');
  return webImplementation;
}

// Export the appropriate implementation
export const electronBridge = getElectronBridge();

// Type definitions are handled in electron.d.ts
