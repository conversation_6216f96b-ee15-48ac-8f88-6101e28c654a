
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";
import CostCalculatorModal from "./CostCalculatorModal";
import DiscoverDifferenceModal from "./DiscoverDifferenceModal";

const CostSavingsSection = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [cardsRef, cardsVisible] = useScrollAnimation(0.1);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.2);
  const { t } = useLanguage();
  const [isCalculatorOpen, setIsCalculatorOpen] = useState(false);
  const [isDifferenceOpen, setIsDifferenceOpen] = useState(false);

  return (
    <>
      <section className="py-24 bg-gradient-to-br from-ai-purple/5 via-transparent to-ai-blue/5">
        <div className="container mx-auto px-6">
          <div 
            ref={headerRef}
            className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
          >
            <h2 className="text-5xl font-bold mb-6 text-gradient text-center">
              {t('cost.title')}
            </h2>
            <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed text-center">
              {t('cost.subtitle')}
            </p>
          </div>
          
          <div 
            ref={cardsRef}
            className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12"
          >
            <Card className={`bg-gradient-to-br from-ai-purple/10 to-ai-violet/10 border-ai-purple/30 hover:glow-box transition-all duration-300 text-center animate-slide-in-left ${cardsVisible ? 'visible' : ''}`}>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-ai-cyan text-center">{t('cost.reduction')}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-lg text-center">
                  {t('cost.reductionDescription')}
                </CardDescription>
              </CardContent>
            </Card>
            
            <Card className={`bg-gradient-to-br from-ai-blue/10 to-ai-cyan/10 border-ai-blue/30 hover:glow-box transition-all duration-300 text-center animate-slide-in-right ${cardsVisible ? 'visible' : ''}`}
                  style={{"--delay": "0.2s"} as React.CSSProperties}>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-ai-pink text-center">{t('cost.saved')}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-lg text-center">
                  {t('cost.savedDescription')}
                </CardDescription>
              </CardContent>
            </Card>
          </div>
          
          <div 
            ref={ctaRef}
            className={`text-center space-y-4 animate-scale-in ${ctaVisible ? 'visible' : ''}`}
          >
            <Button 
              size="lg" 
              className="bg-ai-glow hover:scale-105 transition-all duration-300 px-8 py-4 text-lg animate-glow-pulse mb-4"
              onClick={() => setIsCalculatorOpen(true)}
            >
              {t('cost.seeHowMuch')}
            </Button>
            
            <div>
              <Button 
                variant="outline"
                size="lg"
                className="border-ai-purple hover:bg-ai-purple/10 px-8 py-4 text-lg"
                onClick={() => setIsDifferenceOpen(true)}
              >
                {t('hero.cta2')}
              </Button>
            </div>
          </div>
        </div>
      </section>
      
      <CostCalculatorModal 
        isOpen={isCalculatorOpen} 
        onClose={() => setIsCalculatorOpen(false)} 
      />
      
      <DiscoverDifferenceModal 
        isOpen={isDifferenceOpen} 
        onClose={() => setIsDifferenceOpen(false)} 
      />
    </>
  );
};

export default CostSavingsSection;
