using RecoveryRx.Server.Data.Repositories;
using RecoveryRx.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RecoveryRx.Server.DTOs.Role;

namespace RecoveryRx.Server.Services
{
    public class RoleService : IRoleService
    {
        private readonly IRoleRepository _roleRepository;

        public RoleService(IRoleRepository roleRepository)
        {
            _roleRepository = roleRepository;
        }

        private RoleDto MapRoleToDto(Role role)
        {
            return new RoleDto
            {
                Id = role.Id,
                Name = role.Name,
                Description = role.Description
            };
        }

        public async Task<RoleDto?> GetRoleByIdAsync(Guid id)
        {
            var role = await _roleRepository.GetRoleByIdAsync(id);
            return role == null ? null : MapRoleToDto(role);
        }

        public async Task<RoleDto?> GetRoleByNameAsync(string name)
        {
            var role = await _roleRepository.GetRoleByNameAsync(name);
            return role == null ? null : MapRoleToDto(role);
        }

        public async Task<IEnumerable<RoleDto>> GetAllRolesAsync()
        {
            var roles = await _roleRepository.GetAllRolesAsync();
            return roles.Select(MapRoleToDto);
        }

        public async Task<(RoleDto? Role, string? ErrorMessage)> CreateRoleAsync(CreateRoleDto createRoleDto)
        {
            if (await _roleRepository.RoleExistsAsync(createRoleDto.Name))
            {
                return (null, "Role with the same name already exists.");
            }

            var role = new Role
            {
                Name = createRoleDto.Name,
                Description = createRoleDto.Description
            };

            var createdRole = await _roleRepository.CreateRoleAsync(role);
            return (MapRoleToDto(createdRole), null);
        }

        public async Task<(bool Success, string? ErrorMessage)> UpdateRoleAsync(Guid id, CreateRoleDto updateRoleDto)
        {
            var existingRole = await _roleRepository.GetRoleByIdAsync(id);
            if (existingRole == null)
            {
                return (false, "Role not found.");
            }

            // Check if new name conflicts with another existing role
            if (updateRoleDto.Name != existingRole.Name && await _roleRepository.RoleExistsAsync(updateRoleDto.Name))
            {
                 return (false, "Another role with the same name already exists.");
            }

            existingRole.Name = updateRoleDto.Name;
            existingRole.Description = updateRoleDto.Description;

            var success = await _roleRepository.UpdateRoleAsync(existingRole);
            return (success, success ? null : "Failed to update role.");
        }

        public async Task<bool> DeleteRoleAsync(Guid id)
        {
            // Consider business logic: should we prevent deletion if roles are assigned to users?
            // For now, direct deletion. This might be enhanced later.
            return await _roleRepository.DeleteRoleAsync(id);
        }
    }
}
