import { ipcMain, WebContents, WebFrameMain } from 'electron';
import { pathToFileURL } from 'url';
import { getUIPath } from '../utils/pathResolver.js';
import { isDev } from '../utils/environment.js';

export function ipcMainHandle<Key extends keyof EventPayloadMapping>(
    key: Key,
    handler: () => EventPayloadMapping[Key]
  ) {
    ipcMain.handle(key, (event) => {
      validateEventFrame(event.senderFrame);
      return handler();
    });
  }
  
  export function ipcMainOn<Key extends keyof EventPayloadMapping>(
    key: Key,
    handler: (payload: EventPayloadMapping[Key]) => void
  ) {
    ipcMain.on(key, (event, payload) => {
      validateEventFrame(event.senderFrame);
      return handler(payload);
    });
  }
  
  export function ipcWebContentsSend<Key extends keyof EventPayloadMapping>(
    key: Key,
    webContents: WebContents,
    payload: EventPayloadMapping[Key]
  ) {
    webContents.send(key, payload);
  }
  
  export function validateEventFrame(frame: WebFrameMain | null) {
    if (!frame) {
      throw new Error('Invalid event: frame is null');
    }
    if (isDev() && new URL(frame.url).host === 'localhost:5123') {
      return;
    }
    if (frame.url !== pathToFileURL(getUIPath()).toString()) {
      throw new Error('Malicious event');
    }
  }