
import { useEffect, useRef } from 'react';

const MatrixTrail = () => {
  const trailRef = useRef<HTMLDivElement[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = { x: e.clientX, y: e.clientY };
      
      // Create binary trail element
      const trail = document.createElement('div');
      trail.textContent = Math.random() > 0.5 ? '1' : '0';
      trail.className = 'fixed pointer-events-none text-ai-purple/60 font-mono text-sm animate-fade-out z-10';
      trail.style.left = `${e.clientX}px`;
      trail.style.top = `${e.clientY}px`;
      trail.style.transform = 'translate(-50%, -50%)';
      
      document.body.appendChild(trail);
      
      // Remove after animation
      setTimeout(() => {
        if (trail.parentNode) {
          trail.parentNode.removeChild(trail);
        }
      }, 1000);
    };

    window.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return null;
};

export default MatrixTrail;
