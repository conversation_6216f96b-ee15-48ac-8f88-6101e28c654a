import { Box, Container, Grid, Typography } from '@mui/material';
import { Fade, Slide } from 'react-awesome-reveal';

const PortfolioSection: React.FC = () => {
  const portfolioItems: { title: string; description: string; image: string }[] = [
    { title: 'Modern Marketing Strategies', description: 'Comprehensive digital campaigns for local businesses', image: '/portfolio-1.png' },
    { title: 'Customized Promotional Materials', description: 'Branded collateral that drives customer engagement', image: '/portfolio-2.jpg' },
    { title: 'Local Media Solutions', description: 'Targeted advertising for Nova Scotia businesses', image: '/portfolio-3.jpg' },
  ];

  return (
    <Box sx={{ py: 8, bgcolor: 'background.paper' }}>
      <Container maxWidth="lg">
        <Fade triggerOnce>
          <Typography variant="h2" component="h2" align="center" sx={{ mb: 2, color: 'primary.main' }}>
            What Makes Benza Digital Marketing Different?
          </Typography>
          <Typography variant="body1" align="center" sx={{ mb: 6, maxWidth: '800px', mx: 'auto' }}>
            We combine creative expertise with data-driven strategies to deliver results that exceed expectations
          </Typography>
        </Fade>
        <Grid container spacing={4}>
          {portfolioItems.map((item, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Slide direction="up" triggerOnce delay={index * 200}>
                <Box sx={{ position: 'relative', height: '300px', overflow: 'hidden', boxShadow: 3 }}>
                  <Box component="img" src={item.image} alt={item.title} sx={{ width: '100%', height: '100%', objectFit: 'cover',filter: 'brightness(0.9)', }} />
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      backgroundColor: 'rgba(0, 0, 0, 0.7)', // Dark overlay for better contrast
                      width: '100%',
                      height: '100%',
                      opacity: 0,
                      transition: 'opacity .5s ease',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: 3,
                      '&:hover': { opacity: 1 },
                    }}
                  >
                    <Typography
                      variant="h5"
                      component="h3"
                      sx={{ color: '#FFFFFF', textShadow: '1px 1px 3px rgba(0, 0, 0, 0.8)', mb: 1, textAlign: 'center' }}
                    >
                      {item.title}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{ color: '#FFFFFF', textShadow: '1px 1px 3px rgba(0, 0, 0, 0.8)', textAlign: 'center' }}
                    >
                      {item.description}
                    </Typography>
                  </Box>
                </Box>
              </Slide>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default PortfolioSection;