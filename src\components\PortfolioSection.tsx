
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";
import { Link } from "react-router-dom";

const PortfolioSection = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [gridRef, gridVisible] = useScrollAnimation(0.1);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.2);
  const { t } = useLanguage();

  const caseStudies = [
    {
      title: t('portfolio.ecommerce.title'),
      metric: t('portfolio.ecommerce.metric'),
      description: t('portfolio.ecommerce.description'),
      gradient: "from-ai-purple to-ai-violet"
    },
    {
      title: t('portfolio.healthcare.title'),
      metric: t('portfolio.healthcare.metric'), 
      description: t('portfolio.healthcare.description'),
      gradient: "from-ai-violet to-ai-blue"
    },
    {
      title: t('portfolio.startup.title'),
      metric: t('portfolio.startup.metric'),
      description: t('portfolio.startup.description'),
      gradient: "from-ai-blue to-ai-cyan"
    }
  ];

  return (
    <section id="portfolio" className="py-24">
      <div className="container mx-auto px-6">
        <div 
          ref={headerRef}
          className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
        >
          <h2 className="text-5xl font-bold mb-6 text-gradient text-center">
            {t('portfolio.title')}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto text-center">
            {t('portfolio.subtitle')}
          </p>
        </div>
        
        <div 
          ref={gridRef}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"
        >
          {caseStudies.map((study, index) => (
            <Card 
              key={index}
              className={`group hover:scale-105 transition-all duration-300 bg-card/50 backdrop-blur-sm border-border hover:border-ai-purple/50 hover:glow-box animate-fade-in-up animate-stagger ${gridVisible ? 'visible' : ''}`}
              style={{"--delay": `${index * 0.2}s`} as React.CSSProperties}
            >
              <CardHeader>
                <div className={`w-full h-3 rounded-lg bg-gradient-to-r ${study.gradient} mb-4 group-hover:animate-glow-pulse`}></div>
                <CardTitle className="text-2xl font-bold group-hover:text-gradient transition-all duration-300">
                  {study.title}
                </CardTitle>
                <div className="text-lg font-semibold text-ai-cyan">
                  {study.metric}
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-muted-foreground">
                  {study.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div 
          ref={ctaRef}
          className={`text-center animate-scale-in ${ctaVisible ? 'visible' : ''}`}
        >
          <Link to="/portfolio">
            <Button 
              size="lg" 
              variant="outline"
              className="border-ai-purple hover:bg-ai-purple/10 px-8 py-4 text-lg"
            >
              {t('portfolio.cta')}
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
