import { useAuth } from '../contexts/AuthContext';
import { useUserProfile } from '../hooks/useUserProfile';
import { Link, Outlet } from 'react-router-dom';

const Home = () => {
  const { logout, refresh, isRefreshing, token } = useAuth();
  const { user, loading, error, refetch } = useUserProfile();

  return (
    <div className="flex min-h-screen">
      <div className="w-64 bg-gray-800 text-white p-4">
        <h2 className="text-xl font-bold mb-6">Support Widget Admin</h2>

        {/* User Profile Section */}
        <div className="mb-6 p-3 bg-gray-700 rounded-lg">
          {loading ? (
            <div className="text-sm text-gray-300">Loading profile...</div>
          ) : error ? (
            <div className="text-sm text-red-300">Error loading profile</div>
          ) : user ? (
            <div>
              <div className="text-sm font-medium text-gray-200">{user.username}</div>
              <div className="text-xs text-gray-400">{user.email}</div>
              <div className="flex items-center mt-2">
                <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                  user.status === 'ONLINE' ? 'bg-green-400' : 'bg-gray-400'
                }`}></span>
                <span className="text-xs text-gray-400">{user.status}</span>
              </div>
              <div className="text-xs text-gray-400 mt-1">
                Role: {user.role}
              </div>
            </div>
          ) : null}
        </div>

        <nav>
          <ul>
            <li className="mb-2">
              <Link to="/" className="hover:text-blue-300">Home</Link>
            </li>
            <li className="mb-2">
              <Link to="/agents" className="hover:text-blue-300">Agents</Link>
            </li>
            <li className="mb-2">
              <Link to="/chats" className="hover:text-blue-300">Chats</Link>
            </li>
            <li className="mb-2">
              <Link to="/calls" className="hover:text-blue-300">Calls</Link>
            </li>
            <li className="mb-2">
              <button
                onClick={refresh}
                disabled={isRefreshing}
                className="hover:text-blue-300 disabled:text-gray-500"
              >
                {isRefreshing ? 'Refreshing...' : 'Refresh Token'}
              </button>
            </li>
            <li>
              <button onClick={logout} className="hover:text-blue-300">
                Logout
              </button>
            </li>
          </ul>
        </nav>
      </div>
      <div className="flex-1 p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <button
            onClick={refetch}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Refresh Profile
          </button>
        </div>

        <Outlet />

        {/* Welcome Section */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">
            Welcome{user ? `, ${user.username}` : ''}!
          </h2>
          <p className="text-gray-600 mb-4">
            Manage your tenants, agents, and support interactions here.
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i}>
                    <div className="h-3 bg-gray-200 rounded w-1/3 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Profile</h3>
                <p className="text-red-600">{error}</p>
              </div>
              <button
                onClick={refetch}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        )}

        {/* User Details Card */}
        {user && !loading && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold mb-4">Account Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <p className="mt-1 text-sm text-gray-900">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Username</label>
                <p className="mt-1 text-sm text-gray-900">{user.username}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Role</label>
                <span className="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {user.role}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <div className="mt-1 flex items-center">
                  <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                    user.status === 'ONLINE' ? 'bg-green-400' : 'bg-gray-400'
                  }`}></span>
                  <span className="text-sm text-gray-900">{user.status}</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Account Status</label>
                <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {user.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email Verified</label>
                <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.emailVerified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {user.emailVerified ? 'Verified' : 'Pending'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">2FA Enabled</label>
                <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.twoFactorEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {user.twoFactorEnabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Member Since</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(user.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Debug Section */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-2">Debug Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <label className="block font-medium text-gray-700">Token Status</label>
              <p className="text-gray-600">
                {token ? `${token.substring(0, 20)}...` : 'No token'}
              </p>
            </div>
            <div>
              <label className="block font-medium text-gray-700">Refresh Status</label>
              <p className="text-gray-600">
                {isRefreshing ? 'Refreshing...' : 'Ready'}
              </p>
            </div>
            {user && (
              <div>
                <label className="block font-medium text-gray-700">Tenant ID</label>
                <p className="text-gray-600 break-all">{user.tenantId}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;