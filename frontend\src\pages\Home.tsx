import { useAuth } from '../hooks/useAuth';
import { Link, Outlet } from 'react-router-dom';

const Home = () => {
  const { logout } = useAuth();

  return (
    <div className="flex min-h-screen">
      <div className="w-64 bg-gray-800 text-white p-4">
        <h2 className="text-xl font-bold mb-6">Support Widget Admin</h2>
        <nav>
          <ul>
            <li className="mb-2">
              <Link to="/" className="hover:text-blue-300">Home</Link>
            </li>
            <li className="mb-2">
              <Link to="/agents" className="hover:text-blue-300">Agents</Link>
            </li>
            <li className="mb-2">
              <Link to="/chats" className="hover:text-blue-300">Chats</Link>
            </li>
            <li className="mb-2">
              <Link to="/calls" className="hover:text-blue-300">Calls</Link>
            </li>
            <li>
              <button onClick={logout} className="hover:text-blue-300">
                Logout
              </button>
            </li>
          </ul>
        </nav>
      </div>
      <div className="flex-1 p-6">
        <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
        <Outlet />
        <div>
          <h2>Welcome to the Support Widget Admin Panel</h2>
          <p>Manage your tenants, agents, and support interactions here.</p>
        </div>
      </div>
    </div>
  );
};

export default Home;