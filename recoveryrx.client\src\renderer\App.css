.App {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--background);
  color: var(--foreground);
  border: 1px solid var(--border);
}

/* RTL Support */
.rtl .App {
  direction: rtl;
  text-align: right;
}

/* Language-specific font styling */
.lang-fa {
  font-family: 'Vazir', 'IRANSansWeb', sans-serif !important;
}

/* Window header/titlebar */
.window-header {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  background-color: var(--card);
  -webkit-app-region: drag; /* Makes the header draggable */
  user-select: none;
  border-bottom: 1px solid var(--border);
}

.window-title {
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

.window-controls {
  display: flex;
  -webkit-app-region: no-drag; /* Makes buttons clickable */
}

.window-control-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  color: var(--muted-foreground);
  transition: background-color 0.1s, color 0.1s;
}

.window-control-button:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

.window-control-button.close:hover {
  background-color: var(--destructive);
  color: white;
}

/* Main content area */
.window-content {
  flex: 1;
  overflow: auto;
  padding: 16px;
}
 
/* App container layout */
.app-container {
  display: flex;
  flex: 1;
  height: calc(100vh - 32px);
  position: relative;
  overflow: hidden;
}

/* Sidebar container */
.sidebar-container {
  position: relative;
  z-index: 10;
  transition: width 0.3s ease, opacity 0.3s ease;
}

/* Hidden sidebar */
.sidebar-hidden {
  width: 0 !important;
  overflow: hidden;
  opacity: 0;
}

/* Main container */
.main-container {
  flex: 1;
  overflow: auto;
  position: relative;
  padding: 16px;
  transition: margin 0.3s ease, width 0.3s ease;
}

/* When sidebar is collapsed */
.sidebar-collapsed {
  margin-left: 0 !important;
  width: 100% !important;
}

/* When sidebar is collapsed in RTL mode */
.rtl .sidebar-collapsed {
  margin-right: 0 !important;
  width: 100% !important;
}

/* RTL specific adjustments */
.rtl .app-container {
  flex-direction: row-reverse;
}

.rtl .sidebar-container {
  border-left: 1px solid var(--border);
}

.rtl .main-container {
  margin-right: 0;
}
