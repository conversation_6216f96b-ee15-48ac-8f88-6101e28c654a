
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Users, Target, Lightbulb, Award } from "lucide-react";
import { Link } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BinaryBackground from "@/components/BinaryBackground";
import MatrixTrail from "@/components/MatrixTrail";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";

const About = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [storyRef, storyVisible] = useScrollAnimation(0.1);
  const [valuesRef, valuesVisible] = useScrollAnimation(0.1);
  const [expertiseRef, expertiseVisible] = useScrollAnimation(0.2);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.3);
  const { t } = useLanguage();

  const values = [
    {
      icon: Lightbulb,
      title: t('aboutPage.values.innovation.title'),
      description: t('aboutPage.values.innovation.description'),
      gradient: "from-ai-purple to-ai-violet"
    },
    {
      icon: Target,
      title: t('aboutPage.values.excellence.title'),
      description: t('aboutPage.values.excellence.description'),
      gradient: "from-ai-violet to-ai-blue"
    },
    {
      icon: Users,
      title: t('aboutPage.values.collaboration.title'),
      description: t('aboutPage.values.collaboration.description'),
      gradient: "from-ai-blue to-ai-cyan"
    },
    {
      icon: Award,
      title: t('aboutPage.values.quality.title'),
      description: t('aboutPage.values.quality.description'),
      gradient: "from-ai-cyan to-ai-purple"
    }
  ];

  return (
    <div className="min-h-screen bg-background text-foreground relative">
      <BinaryBackground />
      <MatrixTrail />
      <div className="relative z-20">
        <Header />
        
        <section className="py-24">
          <div className="container mx-auto px-6">
            <div 
              ref={headerRef}
              className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
            >
              <h1 className="text-5xl font-bold mb-6 text-gradient">
                {t('aboutPage.title')}
              </h1>
              <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                {t('aboutPage.subtitle')}
              </p>
            </div>

            <div 
              ref={storyRef}
              className={`mb-20 animate-fade-in-up ${storyVisible ? 'visible' : ''}`}
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30">
                  <CardHeader>
                    <CardTitle className="text-3xl text-gradient">{t('aboutPage.ourStory')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-lg leading-relaxed">
                      {t('aboutPage.storyDescription')}
                    </CardDescription>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-ai-blue/10 to-ai-cyan/10 border-ai-blue/30">
                  <CardHeader>
                    <CardTitle className="text-3xl text-gradient">{t('aboutPage.ourPassion')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-lg leading-relaxed">
                      {t('aboutPage.passionDescription')}
                    </CardDescription>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div 
              ref={valuesRef}
              className={`mb-20 animate-scale-in ${valuesVisible ? 'visible' : ''}`}
            >
              <h2 className="text-4xl font-bold text-center mb-12 text-gradient">{t('aboutPage.ourValues')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {values.map((value, index) => (
                  <Card 
                    key={index}
                    className="group hover:scale-105 transition-all duration-300 bg-card/50 backdrop-blur-sm border-border hover:border-ai-purple/50 hover:glow-box"
                  >
                    <CardHeader className="text-center">
                      <div className={`w-16 h-16 rounded-lg bg-gradient-to-r ${value.gradient} mx-auto mb-4 flex items-center justify-center group-hover:animate-glow-pulse`}>
                        <value.icon className="h-8 w-8 text-white" />
                      </div>
                      <CardTitle className="text-xl">{value.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-center">
                        {value.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div 
              ref={expertiseRef}
              className={`mb-20 animate-slide-in-left ${expertiseVisible ? 'visible' : ''}`}
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h2 className="text-4xl font-bold mb-6 text-gradient">{t('aboutPage.ourExpertise')}</h2>
                  <div className="space-y-6">
                    <p className="text-lg text-muted-foreground leading-relaxed">
                      {t('aboutPage.expertiseDescription1')}
                    </p>
                    <p className="text-lg text-muted-foreground leading-relaxed">
                      {t('aboutPage.expertiseDescription2')}
                    </p>
                  </div>
                </div>

                <Card className="bg-gradient-to-br from-ai-violet/10 to-ai-pink/10 border-ai-violet/30">
                  <CardHeader>
                    <CardTitle className="text-2xl">{t('aboutPage.whyChooseMaxqit')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {t('aboutPage.reasons').map((reason: string, index: number) => (
                        <li key={index} className="flex items-center space-x-3">
                          <div className="w-2 h-2 rounded-full bg-ai-cyan"></div>
                          <span>{reason}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div 
              ref={ctaRef}
              className={`text-center animate-scale-in ${ctaVisible ? 'visible' : ''}`}
            >
              <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30 max-w-4xl mx-auto">
                <CardHeader>
                  <CardTitle className="text-3xl text-gradient">{t('aboutPage.joinJourney')}</CardTitle>
                  <CardDescription className="text-lg">
                    {t('aboutPage.joinDescription')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link to="/contact">
                      <Button 
                        size="lg" 
                        className="bg-ai-glow hover:scale-105 transition-all duration-300 px-8 py-4 text-lg animate-glow-pulse"
                      >
                        {t('aboutPage.connectWithUs')}
                      </Button>
                    </Link>
                    <Link to="/#services">
                      <Button 
                        size="lg" 
                        variant="outline"
                        className="border-ai-purple hover:bg-ai-purple/10 px-8 py-4 text-lg"
                      >
                        {t('aboutPage.exploreServices')}
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default About;
