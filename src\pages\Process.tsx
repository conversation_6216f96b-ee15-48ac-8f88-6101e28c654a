import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BinaryBackground from "@/components/BinaryBackground";
import MatrixTrail from "@/components/MatrixTrail";
import SEO from "@/components/SEO";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { Search, Palette, Code, CheckCircle, Rocket, HeadphonesIcon } from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";

const Process = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [stepsRef, stepsVisible] = useScrollAnimation(0.1);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.2);
  const [expandedStep, setExpandedStep] = useState<number | null>(null);
  const { t } = useLanguage();

  const processSteps = [
    {
      icon: <Search className="h-10 w-10 text-white" />, // Increased size, white for contrast
      title: t('processPage.discovery.title'),
      description: t('processPage.discovery.description'),
      details: t('processPage.discovery.details'),
      gradient: "from-ai-cyan to-ai-blue",
      stats: t('processPage.discovery.stats')
    },
    {
      icon: <Palette className="h-10 w-10 text-white" />,
      title: t('processPage.design.title'),
      description: t('processPage.design.description'),
      details: t('processPage.design.details'),
      gradient: "from-ai-purple to-ai-violet",
      stats: t('processPage.design.stats')
    },
    {
      icon: <Code className="h-10 w-10 text-white" />,
      title: t('processPage.development.title'),
      description: t('processPage.development.description'),
      details: t('processPage.development.details'),
      gradient: "from-ai-blue to-ai-cyan",
      stats: t('processPage.development.stats')
    },
    {
      icon: <CheckCircle className="h-10 w-10 text-white" />,
      title: t('processPage.testing.title'),
      description: t('processPage.testing.description'),
      details: t('processPage.testing.details'),
      gradient: "from-ai-violet to-ai-pink",
      stats: t('processPage.testing.stats')
    },
    {
      icon: <Rocket className="h-10 w-10 text-white" />,
      title: t('processPage.launch.title'),
      description: t('processPage.launch.description'),
      details: t('processPage.launch.details'),
      gradient: "from-ai-pink to-ai-purple",
      stats: t('processPage.launch.stats')
    },
    {
      icon: <HeadphonesIcon className="h-10 w-10 text-white" />,
      title: t('processPage.support.title'),
      description: t('processPage.support.description'),
      details: t('processPage.support.details'),
      gradient: "from-ai-cyan to-ai-blue",
      stats: t('processPage.support.stats')
    }
  ];

  return (
    <div className="min-h-screen bg-background text-foreground relative">
      <SEO 
        title="AI-Powered Development Process | Maxqit - Efficiency Meets Innovation"
        description="Discover Maxqit's cutting-edge AI-enhanced development process. From discovery to support, see how we deliver faster, smarter, and better results with 40% time savings."
        keywords="AI development process, software development methodology, AI-assisted coding, automated testing, smart deployment, agile development"
      />
      
      <BinaryBackground />
      <MatrixTrail />
      <div className="relative z-20">
        <Header />
        
        {/* Hero Section */}
        <section className="py-24">
          <div className="container mx-auto px-6">
            <div 
              ref={headerRef}
              className={`text-center mb-16 transition-all duration-1000 ${headerVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
            >
              <h1 className="text-6xl font-bold mb-6 text-gradient">
                {t('processPage.title')}
              </h1>
              <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-8">
                {t('processPage.subtitle')}
              </p>
              <Link to="/contact">
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-ai-cyan to-ai-blue hover:from-ai-blue hover:to-ai-cyan transition-all duration-300 px-8 py-4 text-lg"
                >
                  {t('processPage.startJourney')}
                </Button>
              </Link>
            </div>

            {/* Detailed Process Steps */}
            <div 
              ref={stepsRef}
              className={`space-y-8 mb-16 transition-all duration-1000 delay-300 ${stepsVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
            >
              <h2 className="text-4xl font-bold text-center mb-12 text-gradient">
                {t('processPage.aiEnhancedProcess')}
              </h2>
              
              {processSteps.map((step, index) => (
                <Card 
                  key={index}
                  className={`bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30 hover:border-ai-cyan/50 transition-all duration-300 cursor-pointer ${
                    expandedStep === index ? 'ring-2 ring-ai-cyan shadow-lg shadow-ai-cyan/20' : ''
                  }`}
                  onClick={() => setExpandedStep(expandedStep === index ? null : index)}
                >
                  <CardHeader>
                    <div className="flex items-center gap-6">
                      <div className={`w-16 h-16 rounded-lg bg-gradient-to-r ${step.gradient} flex items-center justify-center shadow-lg relative`}>
                        {/* Add a subtle dark overlay for better contrast */}
                        <div className="absolute inset-0 bg-black/20 rounded-lg"></div>
                        <div className="relative z-10">
                          {step.icon}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-2xl">{step.title}</CardTitle>
                          <span className="text-ai-cyan font-semibold text-lg">{step.stats}</span>
                        </div>
                        <CardDescription className="text-lg mt-2">
                          {step.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  
                  {expandedStep === index && (
                    <CardContent className="pt-0">
                      <div className="bg-card/50 rounded-lg p-6 mt-4 border border-ai-purple/20">
                        <p className="text-base leading-relaxed">
                          {step.details}
                        </p>
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>

            {/* CTA Section */}
            <div 
              ref={ctaRef}
              className={`text-center transition-all duration-1000 delay-600 ${ctaVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
            >
              <h2 className="text-4xl font-bold mb-6 text-gradient">
                {t('processPage.readyForSmarterProcess')}
              </h2>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                {t('processPage.readySubtitle')}
              </p>
              <Link to="/contact">
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-ai-cyan to-ai-blue hover:from-ai-blue hover:to-ai-cyan transition-all duration-300 px-8 py-4 text-lg"
                >
                  {t('processPage.contactUsToday')}
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default Process;