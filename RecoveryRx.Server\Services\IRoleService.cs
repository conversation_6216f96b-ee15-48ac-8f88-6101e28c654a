using RecoveryRx.Server.DTOs.Role;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Services
{
    public interface IRoleService
    {
        Task<RoleDto?> GetRoleByIdAsync(Guid id);
        Task<RoleDto?> GetRoleByNameAsync(string name);
        Task<IEnumerable<RoleDto>> GetAllRolesAsync();
        Task<(RoleDto? Role, string? ErrorMessage)> CreateRoleAsync(CreateRoleDto createRoleDto);
        Task<(bool Success, string? ErrorMessage)> UpdateRoleAsync(Guid id, CreateRoleDto updateRoleDto); // Reusing CreateRoleDto for update simplicity
        Task<bool> DeleteRoleAsync(Guid id);
    }
}
