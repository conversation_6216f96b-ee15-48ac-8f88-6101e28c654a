import { <PERSON>, <PERSON><PERSON>, Maximize2, Minimize2 } from 'lucide-react';
import { electronBridge } from '../utils/electronBridge';
import { useEffect, useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface WindowHeaderProps {
  children?: React.ReactNode;
}

export function WindowHeader({ children }: WindowHeaderProps) {
  const [isMaximized, setIsMaximized] = useState(false);
  const [showCloseDialog, setShowCloseDialog] = useState(false);

  // Listen for window state changes from Electron
  useEffect(() => {
    // Add event listener for window maximize/unmaximize events
    const handleMaximizeChange = (maximized: boolean) => {
      setIsMaximized(maximized);
    };

    // Setup listeners when component mounts
    if (window.electron) {
      const unsubscribe = window.electron.onWindowStateChange(handleMaximizeChange);
      
      // Get initial window state
      window.electron.isWindowMaximized().then((maximized: boolean) => {
        setIsMaximized(maximized);
      });

      return () => {
        // Clean up listeners when component unmounts
        unsubscribe();
      };
    }
  }, []);

  const handleCloseClick = () => {
    setShowCloseDialog(true);
  };

  const handleMaximizeToggle = () => {
    if (isMaximized) {
      electronBridge.sendFrameAction('UNMAXIMIZE');
    } else {
      electronBridge.sendFrameAction('MAXIMIZE');
    }
  };

  return (
    <div className="window-header">
      <div className="flex items-center gap-4">
        <div className="window-title">RecoveryRx</div>
      </div>
      
      <div className="flex items-center gap-4">
        <div className="mr-4" style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}>{children}</div>
        <div className="window-controls">
          <button
            className="window-control-button"
            onClick={() => electronBridge.sendFrameAction('MINIMIZE')}
            aria-label="Minimize"
          >
            <Minus size={16} />
          </button>
          
          <button
            className="window-control-button"
            onClick={handleMaximizeToggle}
            aria-label={isMaximized ? "Restore" : "Maximize"}
            title={isMaximized ? "Restore" : "Maximize"}
          >
            {isMaximized ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
          </button>
          
          <button
            className="window-control-button close"
            onClick={handleCloseClick}
            aria-label="Close"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      <AlertDialog open={showCloseDialog} onOpenChange={setShowCloseDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Exit RecoveryRx?</AlertDialogTitle>
            <AlertDialogDescription>
              Do you want to close RecoveryRx or keep it running in the system tray?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => electronBridge.sendFrameAction('MINIMIZE_TO_TRAY')}
            >
              Minimize to Tray
            </AlertDialogAction>
            <AlertDialogAction
              onClick={() => electronBridge.sendFrameAction('CLOSE')}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Close Application
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
