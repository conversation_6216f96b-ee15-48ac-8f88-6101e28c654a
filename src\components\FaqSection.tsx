import { Box, Container, Grid, Typography, Card, CardContent } from '@mui/material';
import { Fade } from 'react-awesome-reveal';

const FaqSection: React.FC = () => {
  const faqs: { question: string; answer: string }[] = [
    { question: 'What services does Benza Advertising offer?', answer: 'We offer a comprehensive range of services including professional photography & videography, graphic design & branding, and digital marketing & advertising solutions tailored for small businesses.' },
    { question: 'How can digital marketing help my small business?', answer: 'Digital marketing helps small businesses increase visibility, reach targeted audiences efficiently, build brand awareness, drive website traffic, generate leads, and ultimately increase sales—all with measurable results and often at a lower cost than traditional marketing.' },
    { question: 'Do you work with businesses outside of Nova Scotia?', answer: 'Yes! While we’re based in Bedford, NS, we work with clients throughout Canada. Our digital services can be delivered remotely, making location a non-issue for most of our offerings.' },
    { question: 'What makes your marketing solutions different from others?', answer: 'Our approach combines high-quality visual content creation with strategic marketing expertise specifically tailored for small businesses. We focus on cost-effective solutions that deliver real results, with personalized service and AI-driven strategies that larger agencies often can’t provide.' },
  ];

  return (
    <Box sx={{ py: 8 }}>
      <Container maxWidth="lg">
        <Fade triggerOnce>
          <Typography variant="h2" component="h2" align="center" sx={{ mb: 6, color: 'primary.main' }}>
            Frequently Asked Questions
          </Typography>
        </Fade>
        <Grid container spacing={2}>
          {faqs.map((faq, index) => (
            <Grid item xs={12} key={index}>
              <Fade triggerOnce delay={index * 100}>
                <Card sx={{ mb: 2, boxShadow: 2, '&:hover': { boxShadow: 4 } }}>
                  <CardContent sx={{ p: 3 }}>
                    <Typography variant="h6" component="h3" sx={{ mb: 1, color: 'primary.main', fontWeight: 'bold' }}>
                      {faq.question}
                    </Typography>
                    <Typography variant="body1">{faq.answer}</Typography>
                  </CardContent>
                </Card>
              </Fade>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default FaqSection;