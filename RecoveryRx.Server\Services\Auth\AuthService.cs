using Microsoft.EntityFrameworkCore;
using RecoveryRx.Models;
using RecoveryRx.Server.Data.Repositories;
using RecoveryRx.Server.DTOs.Auth;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Services.Auth
{
    public class AuthService : IAuthService
    {
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IPasswordService _passwordService;
        private readonly ITokenService _tokenService;
        private readonly ILoginActivityService _loginActivityService;

        public AuthService(
            IUserRepository userRepository,
            IRoleRepository roleRepository,
            IPasswordService passwordService,
            ITokenService tokenService,
            ILoginActivityService loginActivityService)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _passwordService = passwordService;
            _tokenService = tokenService;
            _loginActivityService = loginActivityService;
        }

        public async Task<AuthResponseDto> RegisterAsync(RegisterRequestDto registerDto, string ipAddress, string userAgent)
        {
            // Check if user already exists
            if (await _userRepository.UserExistsAsync(registerDto.Username, registerDto.Email))
            {
                throw new InvalidOperationException("Username or email already exists");
            }

            // Create new user
            var user = new User
            {
                Id = Guid.NewGuid(),
                Username = registerDto.Username,
                Email = registerDto.Email,
                PasswordHash = _passwordService.HashPassword(registerDto.Password),
                FirstName = registerDto.FirstName,
                LastName = registerDto.LastName,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            // Save user to database
            var createdUser = await _userRepository.CreateUserAsync(user, registerDto.Password);

            // Assign default role (e.g., "User")
            await _userRepository.AssignRoleToUserAsync(createdUser.Id, "User");

            // Get user roles
            var roles = await _roleRepository.GetRolesForUserAsync(createdUser.Id);

            // Generate JWT token
            var (token, expiration) = await _tokenService.GenerateTokenAsync(createdUser, roles.Select(r => r.Name));

            // Log successful registration
            await _loginActivityService.LogLoginAttemptAsync(
                createdUser.Id, 
                createdUser.Username, 
                ipAddress, 
                userAgent, 
                true, 
                "Registration successful");

            // Return authentication response
            return new AuthResponseDto
            {
                UserId = createdUser.Id,
                Username = createdUser.Username,
                Email = createdUser.Email,
                Token = token,
                Expiration = expiration,
                Roles = roles.Select(r => r.Name)
            };
        }

        public async Task<AuthResponseDto> LoginAsync(LoginRequestDto loginDto, string ipAddress, string userAgent)
        {
            // Find user by username
            var user = await _userRepository.GetUserByUsernameAsync(loginDto.Username);

            // Check if user exists
            if (user == null)
            {
                await _loginActivityService.LogLoginAttemptAsync(
                    Guid.Empty, 
                    loginDto.Username, 
                    ipAddress, 
                    userAgent, 
                    false, 
                    "User not found");
                
                throw new InvalidOperationException("Invalid username or password");
            }

            // Check if user is active
            if (!user.IsActive)
            {
                await _loginActivityService.LogLoginAttemptAsync(
                    user.Id, 
                    user.Username, 
                    ipAddress, 
                    userAgent, 
                    false, 
                    "Account is disabled");
                
                throw new InvalidOperationException("Account is disabled");
            }

            // Verify password
            if (!_passwordService.VerifyPassword(loginDto.Password, user.PasswordHash))
            {
                await _loginActivityService.LogLoginAttemptAsync(
                    user.Id, 
                    user.Username, 
                    ipAddress, 
                    userAgent, 
                    false, 
                    "Invalid password");
                
                throw new InvalidOperationException("Invalid username or password");
            }

            // Get user roles
            var roles = await _roleRepository.GetRolesForUserAsync(user.Id);

            // Generate JWT token
            var (token, expiration) = await _tokenService.GenerateTokenAsync(user, roles.Select(r => r.Name));

            // Log successful login
            await _loginActivityService.LogLoginAttemptAsync(
                user.Id, 
                user.Username, 
                ipAddress, 
                userAgent, 
                true);

            // Return authentication response
            return new AuthResponseDto
            {
                UserId = user.Id,
                Username = user.Username,
                Email = user.Email,
                Token = token,
                Expiration = expiration,
                Roles = roles.Select(r => r.Name)
            };
        }

        public Task<bool> ValidateTokenAsync(string token)
        {
            return Task.FromResult(_tokenService.ValidateToken(token));
        }

        public async Task<AuthResponseDto> GetCurrentUserAsync(Guid userId)
        {
            var user = await _userRepository.GetUserByIdAsync(userId);
            
            if (user == null)
            {
                throw new InvalidOperationException("User not found");
            }

            var roles = await _roleRepository.GetRolesForUserAsync(userId);
            var (token, expiration) = await _tokenService.GenerateTokenAsync(user, roles.Select(r => r.Name));

            return new AuthResponseDto
            {
                UserId = user.Id,
                Username = user.Username,
                Email = user.Email,
                Token = token,
                Expiration = expiration,
                Roles = roles.Select(r => r.Name)
            };
        }
    }
}
