using Microsoft.EntityFrameworkCore;
using RecoveryRx.Models;
using RecoveryRx.Server.DTOs.Auth;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Services.Auth
{
    public class LoginActivityService : ILoginActivityService
    {
        private readonly RecoveryRxContext _context;

        public LoginActivityService(RecoveryRxContext context)
        {
            _context = context;
        }

        public async Task<LoginActivity> LogLoginAttemptAsync(Guid userId, string username, string ipAddress, string userAgent, bool success, string? failureReason = null)
        {
            var loginActivity = new LoginActivity
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Username = username,
                LoginTime = DateTime.UtcNow,
                IpAddress = ipAddress,
                UserAgent = userAgent,
                Success = success,
                FailureReason = failureReason
            };

            await _context.LoginActivities.AddAsync(loginActivity);
            await _context.SaveChangesAsync();

            return loginActivity;
        }

        public async Task<IEnumerable<LoginActivityDto>> GetLoginHistoryForUserAsync(Guid userId, int limit = 10)
        {
            return await _context.LoginActivities
                .Where(la => la.UserId == userId)
                .OrderByDescending(la => la.LoginTime)
                .Take(limit)
                .Select(la => new LoginActivityDto
                {
                    Id = la.Id,
                    UserId = la.UserId,
                    Username = la.Username,
                    LoginTime = la.LoginTime,
                    IpAddress = la.IpAddress,
                    UserAgent = la.UserAgent,
                    Success = la.Success,
                    FailureReason = la.FailureReason
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<LoginActivityDto>> GetRecentLoginAttemptsAsync(int limit = 50)
        {
            return await _context.LoginActivities
                .OrderByDescending(la => la.LoginTime)
                .Take(limit)
                .Select(la => new LoginActivityDto
                {
                    Id = la.Id,
                    UserId = la.UserId,
                    Username = la.Username,
                    LoginTime = la.LoginTime,
                    IpAddress = la.IpAddress,
                    UserAgent = la.UserAgent,
                    Success = la.Success,
                    FailureReason = la.FailureReason
                })
                .ToListAsync();
        }

        public async Task CleanupOldLoginRecordsAsync(int daysToKeep = 90)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
            
            // Find records older than the cutoff date
            var oldRecords = await _context.LoginActivities
                .Where(la => la.LoginTime < cutoffDate)
                .ToListAsync();
            
            if (oldRecords.Any())
            {
                _context.LoginActivities.RemoveRange(oldRecords);
                await _context.SaveChangesAsync();
            }
        }
    }
}
