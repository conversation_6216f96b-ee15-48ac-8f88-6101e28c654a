using Microsoft.EntityFrameworkCore;
using RecoveryRx.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Data.Repositories
{
    public class RoleRepository : IRoleRepository
    {
        private readonly RecoveryRxContext _context;

        public RoleRepository(RecoveryRxContext context)
        {
            _context = context;
        }

        public async Task<Role?> GetRoleByIdAsync(Guid id)
        {
            return await _context.Roles.FindAsync(id);
        }

        public async Task<Role?> GetRoleByNameAsync(string name)
        {
            return await _context.Roles.FirstOrDefaultAsync(r => r.Name == name);
        }

        public async Task<IEnumerable<Role>> GetAllRolesAsync()
        {
            return await _context.Roles.ToListAsync();
        }

        public async Task<Role> CreateRoleAsync(Role role)
        {
            _context.Roles.Add(role);
            await _context.SaveChangesAsync();
            return role;
        }

        public async Task<bool> UpdateRoleAsync(Role role)
        {
            var existingRole = await _context.Roles.FindAsync(role.Id);
            if (existingRole == null) return false;

            _context.Entry(existingRole).CurrentValues.SetValues(role);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteRoleAsync(Guid id)
        {
            var role = await _context.Roles.FindAsync(id);
            if (role == null) return false;

            // Consider implications: what happens to users with this role?
            // For now, just deleting the role. Might need more complex logic
            // like preventing deletion if users are assigned, or reassigning users.
            _context.Roles.Remove(role);
            return await _context.SaveChangesAsync() > 0;
        }
        
        public async Task<bool> RoleExistsAsync(string name)
        {
            return await _context.Roles.AnyAsync(r => r.Name == name);
        }
        
        public async Task<IEnumerable<Role>> GetRolesForUserAsync(Guid userId)
        {
            return await _context.UserRoles
                .Where(ur => ur.UserId == userId)
                .Include(ur => ur.Role)
                .Select(ur => ur.Role)
                .ToListAsync();
        }
    }
}
