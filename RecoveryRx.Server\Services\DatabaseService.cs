using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using RecoveryRx.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Hosting;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using RecoveryRx.Server.DTOs.Database.Requests;
using RecoveryRx.Server.DTOs.Database.Responses;

namespace RecoveryRx.Server.Services;

/// <summary>
/// Defines the contract for database-related operations.
/// </summary>
public interface IDatabaseService
{
    /// <summary>
    /// Asynchronously tests the connection to the database.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains details about the connection attempt.</returns>
    Task<TestConnectionResponse> TestConnectionAsync();

    /// <summary>
    /// Asynchronously initializes the database, ensuring it is created and ready.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains details about the initialization process.</returns>
    Task<InitializeDatabaseResponse> InitializeDatabaseAsync();

    /// <summary>
    /// Asynchronously gets the configured database provider name.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the database provider information.</returns>
    Task<ProviderResponse> GetDatabaseProviderAsync();

    /// <summary>
    /// Asynchronously applies any pending migrations to the database.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains details about the migrations applied.</returns>
    Task<ApplyMigrationsResponse> ApplyMigrationsAsync();

    /// <summary>
    /// Asynchronously updates the database provider settings in appsettings.json.
    /// </summary>
    /// <param name="settings">The new database settings.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains <c>true</c> if the update is successful; otherwise, <c>false</c>.</returns>
    Task<bool> UpdateDatabaseSettingsAsync(UpdateDatabaseSettingsRequest settings);
}

/// <summary>
/// Provides services for interacting with the application database.
/// </summary>
public class DatabaseService : IDatabaseService
{
    private readonly RecoveryRxContext _context;
    private readonly IConfiguration _configuration;
    private readonly ILogger<DatabaseService> _logger;
    private readonly IWebHostEnvironment _env; 

    /// <summary>
    /// Initializes a new instance of the <see cref="DatabaseService"/> class.
    /// </summary>
    /// <param name="context">The application's database context.</param>
    /// <param name="configuration">The application's configuration.</param>
    /// <param name="logger">The logger for this service.</param>
    /// <param name="env">The web host environment.</param>
    public DatabaseService(RecoveryRxContext context, IConfiguration configuration, ILogger<DatabaseService> logger, IWebHostEnvironment env) 
    {
        _context = context;
        _configuration = configuration;
        _logger = logger;
        _env = env; 
    }

    private string GetSanitizedConnectionDetails(string providerName, string? connectionString)
    {
        if (string.IsNullOrEmpty(connectionString))
        {
            return "Connection string not available or not configured.";
        }

        try
        {
            if (providerName.Equals("Sqlite", StringComparison.OrdinalIgnoreCase))
            {
                System.Text.RegularExpressions.Match match = System.Text.RegularExpressions.Regex.Match(connectionString, @"Data Source=([^;]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                return match.Success ? $"SQLite Database: {match.Groups[1].Value}" : "SQLite: Connection details format unrecognized.";
            }
            else if (providerName.Equals("MySql", StringComparison.OrdinalIgnoreCase) || providerName.Equals("Pomelo.EntityFrameworkCore.MySql", StringComparison.OrdinalIgnoreCase))
            {
                System.Text.RegularExpressions.Match serverMatch = System.Text.RegularExpressions.Regex.Match(connectionString, @"Server=([^;]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                System.Text.RegularExpressions.Match dbMatch = System.Text.RegularExpressions.Regex.Match(connectionString, @"Database=([^;]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                System.Text.RegularExpressions.Match portMatch = System.Text.RegularExpressions.Regex.Match(connectionString, @"Port=([^;]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                string server = serverMatch.Success ? serverMatch.Groups[1].Value : "[unknown server]";
                string database = dbMatch.Success ? dbMatch.Groups[1].Value : "[unknown database]";
                string port = portMatch.Success ? portMatch.Groups[1].Value : "[default port]";
                return $"MySQL Server: {server}, Database: {database}, Port: {port}";
            }
            return "Connection details for this provider are not displayed.";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse connection string for details.");
            return "Could not parse connection details.";
        }
    }

    /// <summary>
    /// Asynchronously tests the connection to the database.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains details about the connection attempt.</returns>
    public async Task<TestConnectionResponse> TestConnectionAsync()
    {
        bool isConnected = false;
        string providerName = _configuration["DatabaseSettings:Provider"] ?? "SQLite";
        string? rawConnectionString = null;
        string connectionDetails = "Not available";

        try
        {
            isConnected = await _context.Database.CanConnectAsync();
            if (isConnected)
            {
                rawConnectionString = _context.Database.GetConnectionString();
                connectionDetails = GetSanitizedConnectionDetails(providerName, rawConnectionString);
            }
            else
            {
                connectionDetails = "Failed to connect. Check configuration and database server status.";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connection test failed with an exception.");
            isConnected = false;
            connectionDetails = $"Connection test failed: {ex.Message}";
        }

        return new TestConnectionResponse
        {
            IsConnected = isConnected,
            ProviderName = providerName,
            ConnectionDetails = connectionDetails
        };
    }

    /// <summary>
    /// Asynchronously initializes the database, ensuring it is created and ready.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains details about the initialization process.</returns>
    public async Task<InitializeDatabaseResponse> InitializeDatabaseAsync()
    {
        var response = new InitializeDatabaseResponse();
        response.ProviderName = _configuration["DatabaseSettings:Provider"] ?? "SQLite";
        response.ActionsPerformed = new List<string>();
        string? rawConnectionString = null;

        try
        {
            _logger.LogInformation("Starting database initialization...");
            rawConnectionString = _context.Database.GetConnectionString();
            response.DatabaseDetails = GetSanitizedConnectionDetails(response.ProviderName, rawConnectionString);

            var created = await _context.Database.EnsureCreatedAsync();
            if (created)
            {
                response.ActionsPerformed.Add("Database schema created.");
                _logger.LogInformation("Database schema was created by EnsureCreatedAsync.");
            }
            else
            {
                response.ActionsPerformed.Add("Database schema already existed.");
                _logger.LogInformation("Database schema already existed.");
            }
            response.Message = "Database initialization process completed.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database initialization failed.");
            response.Message = $"Database initialization failed: {ex.Message}";
            response.ActionsPerformed.Add("Initialization failed due to an error.");
        }
        return response;
    }

    /// <summary>
    /// Asynchronously gets the configured database provider name.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the database provider information.</returns>
    public async Task<ProviderResponse> GetDatabaseProviderAsync()
    {
        string providerName = _configuration["DatabaseSettings:Provider"] ?? "SQLite";
        return await Task.FromResult(new ProviderResponse { ProviderName = providerName });
    }

    /// <summary>
    /// Asynchronously applies any pending migrations to the database.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains details about the migrations applied.</returns>
    public async Task<ApplyMigrationsResponse> ApplyMigrationsAsync()
    {
        var response = new ApplyMigrationsResponse();
        response.ProviderName = _configuration["DatabaseSettings:Provider"] ?? "SQLite";
        response.AppliedMigrations = new List<string>();

        try
        {
            _logger.LogInformation("Checking for pending database migrations...");
            var pendingMigrations = (await _context.Database.GetPendingMigrationsAsync()).ToList();

            if (pendingMigrations.Any())
            {
                _logger.LogInformation($"Found {pendingMigrations.Count} pending migration(s): {string.Join(", ", pendingMigrations)}");
                response.AppliedMigrations.AddRange(pendingMigrations);
                await _context.Database.MigrateAsync();
                response.MigrationStatus = $"Successfully applied {pendingMigrations.Count} migration(s).";
                response.Message = "Database migrations applied successfully.";
                _logger.LogInformation("Database migrations applied successfully.");
            }
            else
            {
                response.MigrationStatus = "No pending migrations found.";
                response.Message = "No pending migrations to apply.";
                _logger.LogInformation("No pending migrations found.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Applying database migrations failed.");
            response.Message = $"Failed to apply database migrations: {ex.Message}";
            response.MigrationStatus = "Migration application failed.";
        }
        return response;
    }

    /// <summary>
    /// Asynchronously updates the database provider settings in appsettings.json.
    /// </summary>
    /// <param name="settings">The new database settings.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains <c>true</c> if the update is successful; otherwise, <c>false</c>.</returns>
    public async Task<bool> UpdateDatabaseSettingsAsync(UpdateDatabaseSettingsRequest settings)
    {
        try
        {
            var appSettingsPath = Path.Combine(_env.ContentRootPath, "appsettings.json");
            var json = await File.ReadAllTextAsync(appSettingsPath);
            var jsonObj = JObject.Parse(json);

            var databaseSettings = jsonObj["DatabaseSettings"] as JObject;
            if (databaseSettings == null)
            {
                databaseSettings = new JObject();
                jsonObj["DatabaseSettings"] = databaseSettings;
            }

            var connectionStrings = databaseSettings["ConnectionStrings"] as JObject;
            if (connectionStrings == null)
            {
                connectionStrings = new JObject();
                databaseSettings["ConnectionStrings"] = connectionStrings;
            }

            databaseSettings["Provider"] = settings.Provider;

            if (string.Equals(settings.Provider, "SQLite", StringComparison.OrdinalIgnoreCase) && settings.SqliteSettings != null)
            {
                connectionStrings["SQLite"] = settings.SqliteSettings.DataSource;
                connectionStrings.Remove("MySQL"); 
            }
            else if (string.Equals(settings.Provider, "MySQL", StringComparison.OrdinalIgnoreCase) && settings.MySqlSettings != null)
            {
                var mysql = settings.MySqlSettings;
                connectionStrings["MySQL"] = $"Server={mysql.Server};Database={mysql.Database};User ID={mysql.Username};Password={mysql.Password};Port={mysql.Port};";
                connectionStrings.Remove("SQLite");
            }
            else
            {
                _logger.LogWarning($"Provider '{settings.Provider}' is selected but corresponding settings are missing or provider is not supported for dynamic update. Connection strings not fully updated.");
            }

            await File.WriteAllTextAsync(appSettingsPath, jsonObj.ToString(Newtonsoft.Json.Formatting.Indented));
            _logger.LogInformation("Database settings updated in appsettings.json. Application restart may be required for changes to take full effect.");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update database settings in appsettings.json.");
            return false;
        }
    }
}
