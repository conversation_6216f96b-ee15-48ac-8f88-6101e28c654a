"use client"
import React, { createContext, useState, useMemo, ReactNode } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import rtlPlugin from 'stylis-plugin-rtl';
import theme from '../theme/theme';

interface DirectionContextType {
  direction: 'ltr' | 'rtl';
  toggleDirection: () => void;
}

export const DirectionContext = createContext<DirectionContextType | undefined>(undefined);

interface DirectionProviderProps {
  children: ReactNode;
}

const DirectionProvider: React.FC<DirectionProviderProps> = ({ children }) => {
  const [direction, setDirection] = useState<'ltr' | 'rtl'>('ltr');

  const toggleDirection = () => {
    setDirection((prev) => (prev === 'ltr' ? 'rtl' : 'ltr'));
  };

  const cache = useMemo(() => {
    if (direction === 'rtl') {
      return createCache({
        key: 'rtl',
        stylisPlugins: [rtlPlugin],
      });
    }
    return createCache({ key: 'ltr' });
  }, [direction]);

  const updatedTheme = useMemo(() => {
    return createTheme({
      ...theme,
      direction,
    });
  }, [direction]);

  return (
    <DirectionContext.Provider value={{ direction, toggleDirection }}>
      <CacheProvider value={cache}>
        <ThemeProvider theme={updatedTheme}>
          <div dir={direction}>{children}</div>
        </ThemeProvider>
      </CacheProvider>
    </DirectionContext.Provider>
  );
};

export default DirectionProvider;