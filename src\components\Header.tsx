
import { But<PERSON> } from "@/components/ui/button";
import { Link, useLocation } from "react-router-dom";
import { useLanguage } from "@/contexts/LanguageContext";
import LanguageSelector from "./LanguageSelector";

const Header = () => {
  const location = useLocation();
  const { t } = useLanguage();

  return (
    <header className="fixed top-0 w-full z-50 bg-background/80 backdrop-blur-md border-b border-border">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center">
            <img 
              src="/img/logo-light.svg" 
              alt="Maxqit" 
              className="h-8 w-auto"
            />
          </Link>

          <nav className={`hidden md:flex items-center gap-8`}>
            <Link
              to="/#services"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {t('nav.services')}
            </Link>
            <Link
              to="/about"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {t('nav.about')}
            </Link>
            <Link
              to="/process"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {t('nav.process')}
            </Link>
            <Link
              to="/portfolio"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {t('nav.portfolio')}
            </Link>
            <Link
              to="/contact"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {t('nav.contact')}
            </Link>
          </nav>

          <div className="flex items-center gap-4">
            <LanguageSelector />
            <Link to="/contact">
              <Button className="bg-ai-gradient hover:opacity-90 transition-opacity animate-glow-pulse">
                {t('nav.getStarted')}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
