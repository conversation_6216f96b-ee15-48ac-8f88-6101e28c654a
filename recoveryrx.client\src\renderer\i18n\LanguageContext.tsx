import { createContext, useState, useContext, useEffect, type ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
// Import i18n instance for typing only
import './i18n';

type LanguageContextType = {
  language: string;
  isRTL: boolean;
  changeLanguage: (lang: string) => void;
};

const defaultLanguageContext: LanguageContextType = {
  language: 'en',
  isRTL: false,
  changeLanguage: () => {},
};

const LanguageContext = createContext<LanguageContextType>(defaultLanguageContext);

export const useLanguage = () => useContext(LanguageContext);

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider = ({ children }: LanguageProviderProps) => {
  const { i18n } = useTranslation();
  const [language, setLanguage] = useState(i18n.language || 'en');
  const [isRTL, setIsRTL] = useState(i18n.language === 'fa');

  useEffect(() => {
    // Set RTL attribute on document body based on language
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    
    // Add language-specific class to body for styling
    document.body.classList.remove('lang-en', 'lang-fa');
    document.body.classList.add(`lang-${language}`);
    
    // Add RTL class if needed
    if (isRTL) {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }, [language, isRTL]);

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang).then(() => {
      setLanguage(lang);
      setIsRTL(lang === 'fa');
    });
  };

  return (
    <LanguageContext.Provider value={{ language, isRTL, changeLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};
