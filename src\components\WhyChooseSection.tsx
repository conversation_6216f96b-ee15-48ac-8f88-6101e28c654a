
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";

const WhyChooseSection = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [gridRef, gridVisible] = useScrollAnimation(0.1);
  const { t } = useLanguage();

  const benefits = [
    {
      title: t('whyChoose.aiLeadership.title'),
      description: t('whyChoose.aiLeadership.description'),
      gradient: "from-ai-purple to-ai-violet",
      image: "img/ai-leadership.jpg"
    },
    {
      title: t('whyChoose.creativeExcellence.title'), 
      description: t('whyChoose.creativeExcellence.description'),
      gradient: "from-ai-violet to-ai-blue",
      image: "img/creative-excellence.jpg"
    },
    {
      title: t('whyChoose.costAdvantage.title'),
      description: t('whyChoose.costAdvantage.description'),
      gradient: "from-ai-blue to-ai-cyan",
      image: "img/cost-advantage.jpg"
    },
    {
      title: t('whyChoose.trustedResults.title'),
      description: t('whyChoose.trustedResults.description'),
      gradient: "from-ai-cyan to-ai-pink",
      image: "img/trusted-results.jpg"
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-ai-blue/5 via-transparent to-ai-purple/5">
      <div className="container mx-auto px-6">
        <div 
          ref={headerRef}
          className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
        >
          <h2 className="text-5xl font-bold mb-6 text-gradient text-center">
            {t('whyChoose.title')}
          </h2>
        </div>
        
        <div 
          ref={gridRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
        >
          {benefits.map((benefit, index) => (
            <Card 
              key={index}
              className={`group hover:scale-105 transition-all duration-300 bg-card/50 backdrop-blur-sm border-border hover:border-ai-purple/50 hover:glow-box text-center animate-fade-in-up animate-stagger ${gridVisible ? 'visible' : ''}`}
              style={{"--delay": `${index * 0.15}s`} as React.CSSProperties}
            >
              <CardHeader>
                <img src={benefit.image} alt={benefit.title} className="w-16 h-16 rounded-full object-cover mx-auto mb-4 group-hover:animate-glow-pulse" />
                <CardTitle className="text-xl font-bold group-hover:text-gradient transition-all duration-300">
                  {benefit.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-muted-foreground">
                  {benefit.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseSection;
