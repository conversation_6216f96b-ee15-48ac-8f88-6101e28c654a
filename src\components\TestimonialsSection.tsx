import { Box, Container, Grid, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import StarIcon from '@mui/icons-material/Star';
import { Fade, Slide } from 'react-awesome-reveal';

const TestimonialsSection: React.FC = () => {
  const testimonials: { text: string; author: string; company: string }[] = [
    { text: 'Working with Benza AD transformed our brand’s online presence. Highly recommend!', author: '<PERSON>', company: 'Local Café Owner' },
    { text: 'The photography service was exceptional. Our products look amazing in all our marketing materials now.', author: '<PERSON>', company: 'Artisan Bakery' },
    { text: '<PERSON><PERSON> helped us reach a wider audience with their strategic approach to digital marketing.', author: '<PERSON>', company: 'Boutique Shop' },
  ];

  return (
    <Box sx={{ py: 8 }}>
      <Container maxWidth="lg">
        <Fade triggerOnce>
          <Typography variant="h2" component="h2" align="center" sx={{ mb: 6, color: 'primary.main' }}>
            Check Out These Amazing Client Results
          </Typography>
        </Fade>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Slide direction="left" triggerOnce>
              <Box
                component="img"
                src="/analytics-dashboard.jpg"
                alt="Analytics Dashboard"
                sx={{ width: '100%', height: 'auto', borderRadius: 2, boxShadow: 3 }}
              />
            </Slide>
          </Grid>
          <Grid item xs={12} md={6}>
            <Slide direction="right" triggerOnce cascade damping={0.2}>
              <Card sx={{ mb: 4, boxShadow: 3, p: 2 }}>
                <CardContent>
                  <Typography variant="h5" component="h3" sx={{ mb: 2, color: 'primary.main' }}>
                    Significantly Improved Metrics
                  </Typography>
                  <Typography variant="body1">
                    Our clients typically see 40% increase in engagement and 25% higher conversion rates within the first 3 months of working with our team.
                  </Typography>
                </CardContent>
              </Card>
              <Card sx={{ boxShadow: 3, p: 2 }}>
                <CardContent>
                  <Typography variant="h5" component="h3" sx={{ mb: 2, color: 'primary.main' }}>
                    Growth in Online Visibility
                  </Typography>
                  <Typography variant="body1">
                    We help businesses achieve prominent search rankings and stronger social media presence through our comprehensive digital marketing strategies.
                  </Typography>
                </CardContent>
              </Card>
            </Slide>
          </Grid>
        </Grid>
        <Box sx={{ mt: 8 }}>
          <Fade triggerOnce>
            <Typography variant="h4" component="h3" align="center" sx={{ mb: 6, color: 'primary.main' }}>
              What Our Clients Say
            </Typography>
          </Fade>
          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Fade triggerOnce delay={index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      boxShadow: 3,
                      transition: 'transform 0.3s ease',
                      '&:hover': { transform: 'translateY(-8px)' },
                    }}
                  >
                    <CardContent sx={{ flexGrow: 1, p: 3 }}>
                      <Box sx={{ display: 'flex', mb: 2 }}>
                        {[...Array(5)].map((_, i) => (
                          <StarIcon key={i} sx={{ color: '#FFD700', fontSize: 20 }} />
                        ))}
                      </Box>
                      <Typography variant="body1" sx={{ mb: 3, fontStyle: 'italic' }}>
                        &quot;{testimonial.text}&quot;
                      </Typography>
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {testimonial.author}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {testimonial.company}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
          <Fade triggerOnce delay={600}>
            <Box sx={{ mt: 6, textAlign: 'center' }}>
              <Button href="#contact-us" variant="contained" color="secondary" size="large">
                Let’s Work Together
              </Button>
            </Box>
          </Fade>
        </Box>
      </Container>
    </Box>
  );
};

export default TestimonialsSection;