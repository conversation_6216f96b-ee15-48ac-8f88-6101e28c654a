---
trigger: model_decision
description: "Rules for organizing code in the RecoveryRx .NET Core API server"
---

# RecoveryRx .NET Core API Organization Rules

## Core Architecture

1. **Clean Architecture**:
   - Separate the application into distinct layers (Presentation, Application, Domain, Infrastructure)
   - Maintain proper dependency direction (dependencies should point inward)

2. **Project Structure**:
   - Keep all API code within the `RecoveryRx.Server` directory
   - Follow the established .gitignore patterns for .NET Core projects

## Code Organization

1. **Controllers**:
   - Place controllers in `Controllers` directory
   - Keep controllers thin, focused on HTTP concerns
   - Use proper HTTP status codes and response formats
   - Implement proper route naming conventions

2. **Models**:
   - Separate models into:
     - Domain models (business entities)
     - DTOs (Data Transfer Objects for API requests/responses)
     - View models (if applicable)
   - Use proper validation attributes on DTOs

3. **Services**:
   - Implement business logic in service classes
   - Follow interface-based design for services
   - Use dependency injection for service registration

4. **Data Access**:
   - Use repository pattern for data access
   - Implement proper Entity Framework Core practices
   - Keep database concerns isolated from business logic

5. **Configuration**:
   - Use `appsettings.json` for configuration
   - Create environment-specific settings files
   - Use Options pattern for strongly-typed configuration

## API Design

1. **RESTful Principles**:
   - Follow REST conventions for resource naming
   - Use appropriate HTTP methods (GET, POST, PUT, DELETE)
   - Implement proper resource paths

2. **Versioning**:
   - Implement API versioning strategy
   - Document version changes

3. **Response Format**:
   - Use consistent response format
   - Include appropriate metadata in responses
   - Handle errors with proper status codes and messages

4. **Security**:
   - Implement proper authentication and authorization
   - Validate all input data
   - Protect against common security vulnerabilities

## Cross-Cutting Concerns

1. **Logging**:
   - Use structured logging
   - Log appropriate information at correct levels
   - Avoid logging sensitive data

2. **Error Handling**:
   - Implement global exception handling
   - Return appropriate error responses
   - Log exceptions properly

3. **Validation**:
   - Use FluentValidation or DataAnnotations for input validation
   - Implement validation as a cross-cutting concern

4. **Performance**:
   - Implement caching where appropriate
   - Optimize database queries
   - Use async/await pattern for I/O operations

## Testing

1. **Unit Tests**:
   - Write unit tests for business logic
   - Mock external dependencies
   - Focus on testing behavior, not implementation

2. **Integration Tests**:
   - Test API endpoints with integration tests
   - Use in-memory database for data access tests
   - Test happy path and error scenarios

## Documentation

1. **API Documentation**:
   - Use Swagger/OpenAPI for API documentation
   - Document all endpoints, parameters, and responses
   - Keep documentation up-to-date

2. **Code Documentation**:
   - Use XML comments for public APIs
   - Document complex logic with inline comments
   - Follow consistent documentation style

## Electron Integration

1. **CORS Configuration**:
   - Configure CORS to allow requests from Electron application
   - Implement proper security headers

2. **API Endpoints**:
   - Design endpoints with Electron client in mind
   - Implement proper authentication for Electron client

3. **Communication Pattern**:
   - Remember that Electron main process will communicate with the API
   - Design API responses to be easily consumable by the Electron application
   - Consider the IPC communication flow when designing endpoints