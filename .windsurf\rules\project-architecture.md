---
trigger: model_decision
description: "Overall architecture and integration rules for the RecoveryRx project"
---

# RecoveryRx Project Architecture Rules

## Application Overview

RecoveryRx is a desktop application built with:
- Electron and React for the client-side UI
- .NET Core API for the server-side backend

## Core Architecture Principles

1. **Clear Separation of Concerns**:
   - Frontend (UI): React components in `recoveryrx.client/src/renderer`
   - Desktop Integration: Electron code in `recoveryrx.client/src/electron`
   - Backend: .NET Core API in `RecoveryRx.Server`

2. **Secure Communication Pattern**:
   - UI (React) → Electron Main Process → .NET API → Electron Main Process → UI (React)
   - Never bypass this pattern by making direct API calls from the renderer process

3. **Consistent Styling**:
   - Use shadcn/ui components for all UI elements (see shadcn-ui.md rules)
   - Follow established design tokens and variables

## Integration Points

1. **Electron-React Bridge**:
   - Use the established `electronBridge` utility for all IPC communication
   - Maintain type safety across process boundaries
   - Handle errors consistently at each integration point

2. **Electron-API Communication**:
   - Make API calls from the Electron main process
   - Handle API responses and errors properly
   - Pass data back to the renderer process via IPC

3. **API Response Format**:
   - Use consistent response format across all API endpoints
   - Include appropriate metadata for error handling
   - Design responses to be easily consumable by the Electron client

## Development Workflow

1. **Feature Implementation**:
   - Start with API endpoints in the .NET Core backend
   - Implement Electron main process handlers
   - Create React UI components
   - Connect all layers through the established communication pattern

2. **Testing Strategy**:
   - Test each layer independently
   - Implement integration tests for critical paths
   - Test the full communication flow from UI to API and back

3. **Error Handling**:
   - Implement proper error handling at each layer
   - Provide meaningful error messages to users
   - Log errors appropriately for debugging

## Security Considerations

1. **Content Security Policy**:
   - Follow Electron security best practices
   - Never modify CSP headers to allow direct API access from renderer
   - Use the established IPC pattern for all external communication

2. **API Authentication**:
   - Implement proper authentication for API endpoints
   - Secure sensitive data in transit and at rest
   - Follow principle of least privilege

3. **Input Validation**:
   - Validate all user input at both UI and API layers
   - Sanitize data to prevent injection attacks
   - Implement proper authorization checks

## Performance Guidelines

1. **Responsive UI**:
   - Implement loading states for async operations
   - Use React optimization techniques for complex UIs
   - Consider the impact of IPC communication on UI responsiveness

2. **Efficient API Calls**:
   - Batch API requests when appropriate
   - Implement caching strategies
   - Use pagination for large data sets

3. **Resource Management**:
   - Monitor memory usage in Electron processes
   - Implement proper cleanup for resources
   - Optimize startup time for better user experience

## Documentation Requirements

1. **Code Documentation**:
   - Document public APIs and interfaces
   - Include comments for complex logic
   - Maintain up-to-date documentation for integration points

2. **Architecture Documentation**:
   - Document the overall system architecture
   - Keep communication patterns well-documented
   - Update documentation when architectural changes occur

## Deployment Considerations

1. **Environment Configuration**:
   - Use environment-specific configuration
   - Separate development, testing, and production settings
   - Document environment setup requirements

2. **Build Process**:
   - Maintain consistent build process across environments
   - Automate builds when possible
   - Include proper versioning for releases