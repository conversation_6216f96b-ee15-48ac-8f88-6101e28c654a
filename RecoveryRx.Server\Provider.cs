namespace RecoveryRx.Server;

using SqliteMigrations = RecoveryRx.Server.Migrations.Sqlite;
using MySqlMigrations = RecoveryRx.Server.Migrations.MySql;

public record Provider(string Name, string Assembly)
{
    public static readonly Provider Sqlite = new(nameof(Sqlite), typeof(SqliteMigrations.Marker).Assembly.GetName().Name!);
    public static readonly Provider MySql = new(nameof(MySql), typeof(MySqlMigrations.Marker).Assembly.GetName().Name!);
    //public static readonly Provider SqlServer = new(nameof(SqlServer), typeof(SqlServer.Marker).Assembly.GetName().Name!); // Assuming SqlServer.Marker would need similar aliasing if re-enabled
    //public static readonly Provider Postgres = new(nameof(Postgres), typeof(Postgres.Marker).Assembly.GetName().Name!); // Assuming Postgres.Marker would need similar aliasing if re-enabled
}
