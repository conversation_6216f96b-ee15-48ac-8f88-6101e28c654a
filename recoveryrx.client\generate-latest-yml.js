// Script to generate latest.yml file for Electron auto-updates
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create require function
const require = createRequire(import.meta.url);
const { version } = require('./package.json');

// Configuration
const distPath = path.join(__dirname, 'dist');
const publishPath = path.join(__dirname, 'dist-publish');
const msiFileName = `RecoveryRx ${version}.msi`;
const msiPath = path.join(distPath, msiFileName);
const outputPath = path.join(distPath, 'latest.yml');
const publishOutputPath = path.join(publishPath, 'latest.yml');

// Ensure the publish directory exists
if (!fs.existsSync(publishPath)) {
  fs.mkdirSync(publishPath, { recursive: true });
}

// Generate SHA512 hash of the MSI file
function generateSha512(filePath) {
  const fileBuffer = fs.readFileSync(filePath);
  const hashSum = crypto.createHash('sha512');
  hashSum.update(fileBuffer);
  return hashSum.digest('base64');
}

// Generate the latest.yml file
async function generateLatestYml() {
  try {
    if (!fs.existsSync(msiPath)) {
      console.error(`MSI file not found: ${msiPath}`);
      return;
    }

    const stats = fs.statSync(msiPath);
    const fileSizeInBytes = stats.size;
    const sha512 = generateSha512(msiPath);

    // Format the YML content exactly as electron-updater expects
    const ymlContent = `version: ${version}
files:
  - url: ${msiFileName}
    sha512: ${sha512}
    size: ${fileSizeInBytes}
path: ${msiFileName}
sha512: ${sha512}
releaseDate: ${new Date().toISOString()}
releaseType: release
`;

    fs.writeFileSync(outputPath, ymlContent);
    console.log(`Generated latest.yml at: ${outputPath}`);

    // Copy to publish folder
    fs.writeFileSync(publishOutputPath, ymlContent);
    console.log(`Copied latest.yml to publish folder: ${publishOutputPath}`);
  } catch (error) {
    console.error('Error generating latest.yml:', error);
  }
}

// Execute the function
generateLatestYml().catch(err => {
  console.error('Failed to generate latest.yml:', err);
  process.exit(1);
});
