import { Box, Container, Grid, Typography } from '@mui/material';
import { Fade } from 'react-awesome-reveal';

const OurPartnersSection: React.FC = () => {
  const partners: { name: string; logo: string }[] = [
    { name: 'Partner 1', logo: '/partner-1-logo.webp' },
    { name: 'Partner 2', logo: '/partner-2-logo.png' },
    { name: 'Partner 3', logo: '/partner-3-logo.png' },
    // { name: 'Partner 4', logo: '/partner-4-logo.webp' },
    { name: 'Partner 5', logo: '/partner-5-logo.png' },
  ];

  return (
    <Box sx={{ py: 8, bgcolor: 'background.paper' }}>
      <Container maxWidth="lg">
        <Fade triggerOnce>
          <Typography variant="h2" component="h2" align="center" sx={{ mb: 2, color: 'primary.main' }}>
            Our Partners
          </Typography>
          <Typography variant="body1" align="center" sx={{ mb: 6, maxWidth: '800px', mx: 'auto', color: 'text.secondary' }}>
            We collaborate with trusted partners to deliver exceptional results for your business.
          </Typography>
        </Fade>
        <Grid container spacing={4} justifyContent="center">
          {partners.map((partner, index) => (
            <Grid item xs={6} sm={4} md={3} key={index}>
              <Fade triggerOnce delay={index * 100}>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '120px',
                    bgcolor: 'primary.main',
                    borderRadius: 2,
                    boxShadow: 3,
                    transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out, border-color 0.3s ease-in-out',
                    border: '2px solid',
                    borderColor: 'primary.dark',
                    '&:hover': {
                      transform: 'scale(1.05)',
                      boxShadow: 6,
                      borderColor: 'secondary.main',
                    },
                  }}
                >
                  <Box
                    component="img"
                    src={partner.logo}
                    alt={`${partner.name} Logo`}
                    sx={{
                      maxWidth: '80%',
                      maxHeight: '80%',
                      objectFit: 'contain',
                      filter: 'brightness(1.1)',
                      '&:hover': {
                        filter: 'brightness(1.3) drop-shadow(0 0 4px rgba(0, 228, 178, 0.5))',
                      },
                    }}
                  />
                </Box>
              </Fade>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default OurPartnersSection;