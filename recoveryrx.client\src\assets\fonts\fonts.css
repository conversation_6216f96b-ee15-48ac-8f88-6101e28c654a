/* Vazir <PERSON>ont */
@font-face {
  font-family: 'Vazir';
  src: url('./Vazir/Vazir.woff2') format('woff2'),
       url('./Vazir/Vazir.woff') format('woff'),
       url('./Vazir/Vazir.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Vazir';
  src: url('./Vazir/Vazir-Bold.woff2') format('woff2'),
       url('./Vazir/Vazir-Bold.woff') format('woff'),
       url('./Vazir/Vazir-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Vazir';
  src: url('./Vazir/Vazir-Light.woff2') format('woff2'),
       url('./Vazir/Vazir-Light.woff') format('woff'),
       url('./Vazir/Vazir-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Vazir';
  src: url('./Vazir/Vazir-Medium.woff2') format('woff2'),
       url('./Vazir/Vazir-Medium.woff') format('woff'),
       url('./Vazir/Vazir-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

/* IRANSansWeb Font */
@font-face {
  font-family: 'IRANSansWeb';
  src: url('./IRANSansWeb/IRANSansWeb.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'IRANSansWeb';
  src: url('./IRANSansWeb/IRANSansWeb_Bold.woff2') format('woff2');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'IRANSansWeb';
  src: url('./IRANSansWeb/IRANSansWeb_Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'IRANSansWeb';
  src: url('./IRANSansWeb/IRANSansWeb_Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

/* RTL and Language-specific styles */
.rtl {
  direction: rtl;
  text-align: right;
}

.lang-fa {
  font-family: 'Vazir', 'IRANSansWeb', sans-serif;
}

/* RTL specific adjustments */
.rtl [data-slot="sidebar"] {
  right: 0 !important;
  left: auto !important;
  border-right: none !important;
  border-left: 1px solid var(--sidebar-border) !important;
  position: fixed !important;
  height: calc(100vh - 32px) !important;
  top: 32px !important;
  z-index: 40 !important;
  background-color: var(--background) !important;
}

.rtl [data-slot="sidebar-wrapper"] {
  flex-direction: row-reverse !important;
}

.rtl main {
  margin-right: var(--sidebar-width, 250px) !important;
  margin-left: 0 !important;
  width: calc(100% - var(--sidebar-width, 250px)) !important;
  position: relative !important;
}

.rtl .sidebar-trigger,
.rtl [data-slot="sidebar-trigger"] {
  right: auto !important;
  left: 1rem !important;
  transform: rotate(180deg) !important;
}

/* Flip icons in RTL mode */
.rtl .icon-flip-rtl {
  transform: scaleX(-1);
}

/* RTL adjustments for window controls */
.rtl .window-title {
  margin-left: 0;
  margin-right: 8px;
}

/* RTL adjustments for content */
.rtl .grid {
  direction: rtl;
}

.rtl button,
.rtl .button {
  direction: rtl;
}

/* Fix for flex items in RTL */
.rtl .flex-row {
  flex-direction: row-reverse;
}

.rtl .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

.rtl .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

.rtl .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

.rtl .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

.rtl .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

.rtl .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}
