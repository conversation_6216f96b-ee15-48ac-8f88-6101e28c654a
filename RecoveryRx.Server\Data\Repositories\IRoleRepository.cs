using RecoveryRx.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Data.Repositories
{
    public interface IRoleRepository
    {
        Task<Role?> GetRoleByIdAsync(Guid id);
        Task<Role?> GetRoleByNameAsync(string name);
        Task<IEnumerable<Role>> GetAllRolesAsync();
        Task<Role> CreateRoleAsync(Role role);
        Task<bool> UpdateRoleAsync(Role role);
        Task<bool> DeleteRoleAsync(Guid id);
        Task<bool> RoleExistsAsync(string name);
        Task<IEnumerable<Role>> GetRolesForUserAsync(Guid userId);
    }
}
