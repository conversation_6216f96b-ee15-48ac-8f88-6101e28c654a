# GitHub Release and Auto-Update Setup for RecoveryRx

This guide explains how to use the enhanced build script to publish releases to the dedicated GitHub releases repository (`RecoveryRx-releases`) and enable auto-updates for your RecoveryRx application.

## Prerequisites

1. A GitHub Personal Access Token with the following permissions:
   - `repo` scope for private repositories (if your releases repository is private)
   - `public_repo` scope for public repositories (if your releases repository is public)

2. Your application version in `package.json` must be incremented before each release

3. Access to the dedicated releases repository: https://github.com/keleshteri/RecoveryRx-releases

## Creating a GitHub Personal Access Token

1. Go to [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
2. Click "Generate new token" (classic)
3. Give it a descriptive name like "RecoveryRx Auto-Update"
4. Select the `repo` scope (for private repositories)
5. Click "Generate token"
6. **Important**: Copy the token immediately and store it securely. You won't be able to see it again!

## Using the Build Script

The enhanced build script (`build-publish.ps1`) now supports publishing releases to GitHub. Here's how to use it:

### Basic Build (No Publishing)

```powershell
.\build-publish.ps1
```

This will build the application without publishing to GitHub.

### Build and Publish to GitHub

```powershell
.\build-publish.ps1 -PublishToGitHub -GitHubToken "your_github_token"
```

Or, set the token as an environment variable:

```powershell
$env:GH_TOKEN = "your_github_token"
.\build.ps1 -PublishToGitHub
```

### Additional Options

```powershell
.\build.ps1 -PublishToGitHub -GitHubToken "your_github_token" -ReleaseNotes "What's new in this version" -Draft -Prerelease
```

- `-ReleaseNotes`: Specify release notes (default: "New release")
- `-Draft`: Create as a draft release
- `-Prerelease`: Mark as a pre-release

## Auto-Update Process

1. **Increment Version**: Update the version in `package.json` (e.g., from "1.0.0" to "1.0.1")
2. **Build and Publish**: Run the build script with the `-PublishToGitHub` flag
3. **Verification**: The script will:
   - Check if a release with this version already exists
   - Create a new release if needed
   - Upload the installer and `latest.yml` file

## Troubleshooting

### Release Already Exists

If you see "Release vX.X.X already exists on GitHub", you need to increment the version in `package.json`.

### Authentication Errors

If you see "Error creating GitHub release" with a 401 status code, your token may be invalid or expired.

### Missing Files

If you see "Warning: No installer found to upload" or "Warning: latest.yml not found", check that the build process completed successfully.

## Testing Auto-Updates

To test the auto-update functionality:

1. Build and publish version 1.0.0
2. Install this version on a test machine
3. Increment the version in `package.json` to 1.0.1
4. Build and publish the new version
5. Launch the version 1.0.0 app - it should detect and download the update

## Private Repository Considerations

Since your repository is private, you must:

1. Always use a GitHub token with the `repo` scope
2. Ensure the token has sufficient permissions
3. Keep the token secure and never commit it to your repository

The auto-update system will work with private repositories as long as a valid token is provided.
