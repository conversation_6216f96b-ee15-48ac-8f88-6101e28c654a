/**
 * Environment utilities for the Electron application
 */

/**
 * Check if the application is running in development mode
 */
export function isDev(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Get application environment information
 */
export function getEnvironmentInfo() {
  return {
    nodeEnv: process.env.NODE_ENV || 'production',
    isDev: isDev(),
    platform: process.platform,
    arch: process.arch,
  };
}

/**
 * Get application URL
 */
export function getURL(): string {
  return process.env.APP_URL || 'https://localhost:5123';
}
