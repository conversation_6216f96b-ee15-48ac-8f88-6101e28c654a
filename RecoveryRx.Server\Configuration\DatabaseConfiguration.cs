using Microsoft.EntityFrameworkCore;
using RecoveryRx.Models; 
using RecoveryRx.Server.Data;
using RecoveryRx.Server.Services;

namespace RecoveryRx.Server.Configuration;

/// <summary>
/// Provides extension methods for configuring database-related services.
/// </summary>
public static class DatabaseConfiguration
{
    /// <summary>
    /// Configures the database context and related services for the application.
    /// </summary>
    /// <param name="services">The <see cref="IServiceCollection"/> to add services to.</param>
    /// <param name="configuration">The application's <see cref="IConfiguration"/>.</param>
    /// <param name="args">The command-line arguments passed to the application. Used for overriding provider.</param>
    /// <exception cref="InvalidOperationException">Thrown if an unsupported database provider is specified or connection string is missing.</exception>
    public static void ConfigureDatabase(this IServiceCollection services, IConfiguration configuration, string[] args)
    {
        // Determine provider: Command line (--provider=Value) > appsettings.json (DatabaseSettings:Provider) > default (SQLite)
        // CreateBuilder(args) in Program.cs makes command-line args available via IConfiguration
        string provider = configuration.GetValue<string>("provider") 
                          ?? configuration["DatabaseSettings:Provider"]
                          ?? Provider.Sqlite.Name; // Default to SQLite

        Console.WriteLine($"Selected Database Provider: {provider}");

        string? connectionString = configuration[$"DatabaseSettings:ConnectionStrings:{provider}"] 
                                   ?? configuration.GetConnectionString(provider); // Fallback for simpler config structure

        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException($"Connection string for provider '{provider}' not found in DatabaseSettings:ConnectionStrings or ConnectionStrings section.");
        }
        Console.WriteLine($"Using Connection String: {connectionString}");

        services.AddDbContext<RecoveryRxContext>(options =>
        {
            if (provider.Equals(Provider.Sqlite.Name, StringComparison.OrdinalIgnoreCase))
            {
                options.UseSqlite(
                    connectionString,
                    sqlOptions => sqlOptions.MigrationsAssembly(Provider.Sqlite.Assembly)
                );
            }
            else if (provider.Equals(Provider.MySql.Name, StringComparison.OrdinalIgnoreCase))
            {
                options.UseMySql(
                    connectionString,
                    ServerVersion.AutoDetect(connectionString),
                    mysqlOptions => mysqlOptions.MigrationsAssembly(Provider.MySql.Assembly)
                );
            }
            else
            {
                throw new InvalidOperationException($"Unsupported database provider: {provider}");
            }
        });
        
        // Register Database Service
        services.AddScoped<IDatabaseService, DatabaseService>();
    }
}
