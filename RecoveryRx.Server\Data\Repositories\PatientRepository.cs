using Microsoft.EntityFrameworkCore;
using RecoveryRx.Models;

namespace RecoveryRx.Server.Data.Repositories;

public class PatientRepository : IPatientRepository
{
    private readonly RecoveryRxContext _context;

    public PatientRepository(RecoveryRxContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<Patient>> GetAllAsync()
    {
        return await _context.Patients
            .Where(p => p.IsActive)
            .OrderBy(p => p.LastName)
            .ThenBy(p => p.FirstName)
            .ToListAsync();
    }

    public async Task<Patient?> GetByIdAsync(long id)
    {
        return await _context.Patients.FindAsync(id);
    }

    public async Task<Patient> CreateAsync(Patient patient)
    {
        patient.CreatedAt = DateTime.UtcNow;
        patient.IsActive = true;
        
        _context.Patients.Add(patient);
        await _context.SaveChangesAsync();
        
        return patient;
    }

    public async Task<Patient?> UpdateAsync(Patient patient)
    {
        var existingPatient = await _context.Patients.FindAsync(patient.Id);
        
        if (existingPatient == null)
        {
            return null;
        }

        // Update properties
        existingPatient.FirstName = patient.FirstName;
        existingPatient.LastName = patient.LastName;
        existingPatient.NationalIdNumber = patient.NationalIdNumber;
        existingPatient.Gender = patient.Gender;
        existingPatient.DateOfBirth = patient.DateOfBirth;
        existingPatient.MobileNumber = patient.MobileNumber;
        existingPatient.LandlineNumber = patient.LandlineNumber;
        existingPatient.Address = patient.Address;
        existingPatient.ImagePath = patient.ImagePath;
        existingPatient.UpdatedAt = DateTime.UtcNow;
        
        await _context.SaveChangesAsync();
        
        return existingPatient;
    }

    public async Task<bool> DeleteAsync(long id)
    {
        var patient = await _context.Patients.FindAsync(id);
        
        if (patient == null)
        {
            return false;
        }

        // Soft delete
        patient.IsActive = false;
        patient.UpdatedAt = DateTime.UtcNow;
        
        await _context.SaveChangesAsync();
        
        return true;
    }
}
