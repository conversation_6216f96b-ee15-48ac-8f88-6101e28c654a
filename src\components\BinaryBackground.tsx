
import { useEffect, useRef } from 'react';

interface Particle {
  x: number;
  y: number;
  opacity: number;
  speed: number;
  char: string;
}

const BinaryBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });
  const animationRef = useRef<number>();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const createParticles = () => {
      const particles: Particle[] = [];
      const particleCount = Math.floor((canvas.width * canvas.height) / 15000);
      
      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          opacity: Math.random() * 0.5 + 0.1,
          speed: Math.random() * 2 + 0.5,
          char: Math.random() > 0.5 ? '1' : '0'
        });
      }
      
      particlesRef.current = particles;
    };

    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = { x: e.clientX, y: e.clientY };
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const particles = particlesRef.current;
      const mouse = mouseRef.current;
      
      particles.forEach((particle) => {
        // Calculate distance from mouse
        const dx = mouse.x - particle.x;
        const dy = mouse.y - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // Create ripple effect around mouse
        const maxDistance = 200;
        let rippleOpacity = 0;
        
        if (distance < maxDistance) {
          rippleOpacity = (1 - distance / maxDistance) * 0.8;
          
          // Add slight movement away from mouse
          const angle = Math.atan2(dy, dx);
          particle.x -= Math.cos(angle) * 0.5;
          particle.y -= Math.sin(angle) * 0.5;
        }
        
        // Natural floating movement
        particle.y -= particle.speed;
        particle.x += Math.sin(particle.y * 0.01) * 0.5;
        
        // Reset particle if it goes off screen
        if (particle.y < -10) {
          particle.y = canvas.height + 10;
          particle.x = Math.random() * canvas.width;
        }
        if (particle.x < -10) particle.x = canvas.width + 10;
        if (particle.x > canvas.width + 10) particle.x = -10;
        
        // Set opacity based on distance from mouse and base opacity
        const finalOpacity = Math.max(particle.opacity, rippleOpacity);
        
        // Draw particle with gradient effect near mouse
        if (distance < maxDistance) {
          const gradient = ctx.createRadialGradient(
            particle.x, particle.y, 0,
            particle.x, particle.y, 20
          );
          gradient.addColorStop(0, `rgba(99, 102, 241, ${finalOpacity})`);
          gradient.addColorStop(0.5, `rgba(139, 92, 246, ${finalOpacity * 0.7})`);
          gradient.addColorStop(1, `rgba(59, 130, 246, ${finalOpacity * 0.3})`);
          ctx.fillStyle = gradient;
        } else {
          ctx.fillStyle = `rgba(99, 102, 241, ${finalOpacity})`;
        }
        
        ctx.font = '14px "Courier New", monospace';
        ctx.fillText(particle.char, particle.x, particle.y);
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    createParticles();
    animate();

    window.addEventListener('resize', () => {
      resizeCanvas();
      createParticles();
    });
    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ background: 'transparent' }}
    />
  );
};

export default BinaryBackground;
