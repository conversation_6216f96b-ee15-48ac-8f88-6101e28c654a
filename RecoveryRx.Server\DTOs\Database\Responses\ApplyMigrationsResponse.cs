using System.Collections.Generic;

namespace RecoveryRx.Server.DTOs.Database.Responses
{
    /// <summary>
    /// Represents the response for the apply migrations request.
    /// </summary>
    public class ApplyMigrationsResponse
    {
        /// <summary>
        /// Gets or sets a message indicating the outcome of the migration process.
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the name of the database provider for which migrations were applied.
        /// </summary>
        public string? ProviderName { get; set; }

        /// <summary>
        /// Gets or sets a list of migrations that were successfully applied.
        /// If no migrations were pending or applied, this might be empty or null.
        /// </summary>
        public List<string>? AppliedMigrations { get; set; }

        /// <summary>
        /// Gets or sets a status message regarding pending migrations (e.g., "No pending migrations found.").
        /// </summary>
        public string? MigrationStatus { get; set; }
    }
}
