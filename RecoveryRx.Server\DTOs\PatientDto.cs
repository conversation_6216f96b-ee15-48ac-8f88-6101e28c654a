using System.ComponentModel.DataAnnotations;
using RecoveryRx.Models;

namespace RecoveryRx.Server.DTOs;

public class PatientDto
{
    public long Id { get; set; }
    
    public string FirstName { get; set; } = string.Empty;
    
    public string LastName { get; set; } = string.Empty;
    
    public string? NationalIdNumber { get; set; }
    
    public Gender Gender { get; set; }
    
    public string? GenderName { get; set; }
    
    public DateTime DateOfBirth { get; set; }
    
    public int Age => CalculateAge(DateOfBirth);
    
    public string? MobileNumber { get; set; }
    
    public string? LandlineNumber { get; set; }
    
    public string? Address { get; set; }
    
    public string? ImagePath { get; set; }
    
    public DateTime CreatedAt { get; set; }
    
    public DateTime? UpdatedAt { get; set; }
    
    private int CalculateAge(DateTime birthDate)
    {
        var today = DateTime.Today;
        var age = today.Year - birthDate.Year;
        
        if (birthDate.Date > today.AddYears(-age))
        {
            age--;
        }
        
        return age;
    }
}
