import { BrowserWindow, app } from 'electron';
import path from 'path';
import { isDev } from '../utils/environment.js';

let splashScreen: BrowserWindow | null = null;

export function createSplashScreen(): BrowserWindow {
  // Get app version from package.json
  const appVersion = app.getVersion();
  
  // Create a new browser window for the splash screen
  splashScreen = new BrowserWindow({
    width: 400,
    height: 300,
    transparent: true,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // Load the splash screen HTML file
  const splashPath = isDev() 
    ? path.join(process.cwd(), 'src', 'electron','ui', 'splash.html')
    : path.join(app.getAppPath(), 'dist-electron', 'ui', 'splash.html');
  
  // Pass the app version as a URL hash parameter
  splashScreen.loadFile(splashPath, { hash: appVersion });
  
  // Set the logo path once the splash screen is loaded
  splashScreen.webContents.on('did-finish-load', () => {
    if (!splashScreen) return;
    
    // In production, try multiple possible paths for the logo
    let logoPath = '';
    
    if (isDev()) {
      logoPath = path.join(process.cwd(), 'src', 'assets', 'icon.png');
    } else {
      // Try different possible paths in production
      const possiblePaths = [
        path.join(app.getAppPath(), 'src', 'assets', 'icon.png'),
        path.join(app.getAppPath(), '..', 'src', 'assets', 'icon.png'),
        path.join(app.getAppPath(), '..', '..', 'src', 'assets', 'icon.png'),
        path.join(process.resourcesPath, 'src', 'assets', 'icon.png'),
        path.join(process.resourcesPath, 'app.asar.unpacked', 'src', 'assets', 'icon.png'),
        path.join(process.resourcesPath, 'app.asar.unpacked', 'dist-electron', 'assets', 'icon.png')
      ];
      
      // Log all paths for debugging
      console.log('Checking possible logo paths:', possiblePaths);
      
      // Use the first path that exists
      for (const p of possiblePaths) {
        try {
          if (require('fs').existsSync(p)) {
            logoPath = p;
            console.log('Found logo at:', logoPath);
            break;
          }
        } catch (err) {
          console.error('Error checking path:', p, err);
        }
      }
      
      // If no path exists, use a fallback
      if (!logoPath) {
        console.warn('Could not find logo, using first path as fallback');
        logoPath = possiblePaths[0];
      }
    }
    
    // Convert the file path to a file:// URL format which is safer for IPC
    const logoUrl = `file://${logoPath.replace(/\\/g, '/')}`;
    console.log('Using logo URL:', logoUrl);
    
    // Set the image source using a simpler approach to avoid object cloning issues
    splashScreen.webContents.executeJavaScript(`
      (function() {
        const imgElement = document.getElementById('app-logo');
        const fallbackElement = document.getElementById('logo-fallback');
        
        // Try to load the image with the file:// protocol
        imgElement.src = ${JSON.stringify(logoUrl)};
        
        // Show the image and hide the fallback if it loads successfully
        imgElement.onload = function() {
          imgElement.style.display = 'block';
          fallbackElement.style.display = 'none';
        };
        
        // Keep the fallback visible if the image fails to load
        imgElement.onerror = function() {
          console.error('Failed to load logo image');
          imgElement.style.display = 'none';
          fallbackElement.style.display = 'block';
        };
        
        return 'Logo path set successfully';
      })();
    `).catch(err => console.error('Error setting logo path:', err));
  });

  // Close the splash screen when the main window is ready
  splashScreen.on('closed', () => {
    splashScreen = null;
  });

  return splashScreen;
}

export function closeSplashScreen(): void {
  if (splashScreen) {
    splashScreen.close();
    splashScreen = null;
  }
}
