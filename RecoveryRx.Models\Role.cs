using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace RecoveryRx.Models;

public class Role
{
    [Key]
    public Guid Id { get; set; }

    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    public string? Description { get; set; }

    // Navigation property for UserRoles
    public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
}
