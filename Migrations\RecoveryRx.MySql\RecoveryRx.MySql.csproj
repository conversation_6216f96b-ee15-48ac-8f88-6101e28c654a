<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>RecoveryRx.Server.Migrations.MySql</RootNamespace>
    <AssemblyName>RecoveryRx.Server.Migrations.MySql</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.13">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.13" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\RecoveryRx.Models\RecoveryRx.Models.csproj" />
  </ItemGroup>
 

</Project>