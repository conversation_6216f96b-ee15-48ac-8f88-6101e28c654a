using Microsoft.AspNetCore.Mvc;
using RecoveryRx.Server.DTOs.Role;
using RecoveryRx.Server.Services;
using System;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class RolesController : ControllerBase
    {
        private readonly IRoleService _roleService;

        public RolesController(IRoleService roleService)
        {
            _roleService = roleService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllRoles()
        {
            var roles = await _roleService.GetAllRolesAsync();
            return Ok(roles);
        }

        [HttpGet("{id:guid}")]
        public async Task<IActionResult> GetRoleById(Guid id)
        {
            var role = await _roleService.GetRoleByIdAsync(id);
            if (role == null)
            {
                return NotFound($"Role with ID {id} not found.");
            }
            return Ok(role);
        }

        [HttpGet("name/{name}")]
        public async Task<IActionResult> GetRoleByName(string name)
        {
            var role = await _roleService.GetRoleByNameAsync(name);
            if (role == null)
            {
                return NotFound($"Role with name '{name}' not found.");
            }
            return Ok(role);
        }

        [HttpPost]
        public async Task<IActionResult> CreateRole([FromBody] CreateRoleDto createRoleDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var (role, errorMessage) = await _roleService.CreateRoleAsync(createRoleDto);
            if (role == null)
            {
                return BadRequest(errorMessage ?? "Failed to create role.");
            }
            return CreatedAtAction(nameof(GetRoleById), new { id = role.Id }, role);
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateRole(Guid id, [FromBody] CreateRoleDto updateRoleDto) // Reusing CreateRoleDto
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var (success, errorMessage) = await _roleService.UpdateRoleAsync(id, updateRoleDto);
            if (!success)
            {
                 if (errorMessage != null && errorMessage.Contains("not found")) 
                    return NotFound(errorMessage);
                return BadRequest(errorMessage ?? "Failed to update role.");
            }
            return NoContent();
        }

        [HttpDelete("{id:guid}")]
        public async Task<IActionResult> DeleteRole(Guid id)
        {
            var success = await _roleService.DeleteRoleAsync(id);
            if (!success)
            {
                return NotFound($"Role with ID {id} not found or could not be deleted.");
            }
            return NoContent();
        }
    }
}
