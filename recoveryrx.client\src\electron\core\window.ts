import { app, BrowserWindow, dialog, ipcMain, shell } from "electron";
import { getPreloadPath, getUIPath } from "../utils/pathResolver.js";
import { getURL, isDev } from "../utils/environment.js";

let mainWindow: BrowserWindow | null = null;

// Helper function for minimize to tray logic
function _minimizeToTray() {
    const win = getMainWindow();
    if (win && !win.isDestroyed()) {
        win.hide();
        if (process.platform === 'darwin') {
            // On macOS, hide the dock icon when all windows are closed
            if (app.dock) {
                app.dock.hide();
            }
        } else if (process.platform === 'win32') {
            // On Windows, minimize to tray
            win.setSkipTaskbar(true);
        }
        // Optional: Notify the renderer that the window was minimized to tray
        // if (!win.isDestroyed() && win.webContents) {
        //     win.webContents.send('window-minimized-to-tray', true);
        // }
    }
}

/**
 * Default window configuration
 */
const DEFAULT_WINDOW_CONFIG = {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    backgroundColor: '#ffffff', // Prevent white flash during load
    show: false, // Hide until ready
    frame: false, // Frame less window for custom title bar
    webPreferences: {
        preload: getPreloadPath(),
        // Security settings
        nodeIntegration: false,
        // Enable context isolation
        contextIsolation: true,
        // Enable sandboxing
        sandbox: true,
    },
};

/**
 * Create the main application window
 */
export function createMainWindow(): BrowserWindow {
    // Check if window already exists (prevent multiple windows)
    if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.focus();
        return mainWindow;
    }
    try {
        const window = new BrowserWindow(DEFAULT_WINDOW_CONFIG);
        // Load the app
        if (isDev()) {
            window.loadURL(getURL()).catch(err => {
                console.error('Failed to load dev URL:', err);
            });

            // Open DevTools automatically in development mode
            window.webContents.openDevTools({ mode: 'detach' });
        } else {
            window.loadFile(getUIPath()).catch(err => {
                console.error('Failed to load UI path:', err);
                dialog.showErrorBox('Application Error', 'Failed to load application interface.');
            });
        }
        //TODO: review this if we need to open external links in the default browser instead of a new Electron window
        // Handle external links
        // this will open external links in the default browser instead of a new Electron window
        window.webContents.setWindowOpenHandler(({ url }) => {
            // Open external URLs in the default browser instead of a new Electron window
            if (url.startsWith('http:') || url.startsWith('https:')) {
                shell.openExternal(url).catch(err => {
                    console.error('Failed to open external URL:', err);
                });
            }
            return { action: 'deny' };
        });

        // Store reference to window
        mainWindow = window;

        return window;
    } catch (error) {
        console.error('Error creating main window:', error);
        dialog.showErrorBox('Application Error', 'Failed to create application window.');
        app.quit();
        throw error; // Re-throw for upstream error handling
    }
}

/**
 * Get the main window instance
 */
export function getMainWindow(): BrowserWindow | null {
    return mainWindow;
}

/**
 * Handles the 'MINIMIZE_TO_TRAY' action.
 */
export function handleMinimizeToTrayAction(): void {
    _minimizeToTray();
}

/**
 * Handles the 'CLOSE' action to quit the application.
 */
export function handleCloseAction(): void {
    app.quit(); // This triggers 'before-quit' where willQuitApp is set.
}

/**
 * Handle window close events
 */
export function setupWindowCloseHandlers(window: BrowserWindow): void {
    // Track whether the application is actually quitting
    // This prevents the window from being hidden when the app is truly quitting
    let willQuitApp = false;
    // Handle the window close event
    window.on('close', (e) => {
        // If the app is actually quitting, allow the window to close
        if (willQuitApp) {
            return;
        }
        // Otherwise, prevent the default close behavior
        e.preventDefault();
        try {
            // Hide the window instead of closing it (minimize to tray behavior)
            _minimizeToTray();
        } catch (error) {
            console.error('Error handling window close:', error);
        }
    });

    // When the app is about to quit, set the flag to allow window closing
    app.on('before-quit', () => {
        willQuitApp = true;
    });

    // Reset the quit flag when the window is shown again
    window.on('show', () => {
        willQuitApp = false;

        // Platform-specific behaviors when showing the window
        if (process.platform === 'darwin') {
            // On macOS, show the dock icon when a window is visible
            if (app.dock && !app.dock.isVisible()) {
                app.dock.show();
            }
        } else if (process.platform === 'win32') {
            // On Windows, show in taskbar
            window.setSkipTaskbar(false);
        }
    });
    // Set up an IPC handler for quitting the application
    // This allows other parts of the application to request a proper quit
    ipcMain.handle('quit-application', () => {
        willQuitApp = true;
        app.quit();
        return true;
    });

    // Listen for the standard 'will-quit' event as a backup
    app.on('will-quit', () => {
        willQuitApp = true;
    });
}