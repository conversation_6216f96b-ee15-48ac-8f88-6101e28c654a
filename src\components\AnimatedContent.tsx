"use client";

import { Box, Typography, Button } from '@mui/material';
import { Slide, Reveal } from 'react-awesome-reveal';
import { keyframes } from '@emotion/react';

const customFade = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
`;

interface AnimatedContentProps {
  textItems: { type: string; content: string | string[] }[];
}

const AnimatedContent: React.FC<AnimatedContentProps> = ({ textItems }) => {
  return (
    <>
      {textItems.map((item, index) => (
        <Box key={index} sx={{ mb: 2 }}>
          {item.type === 'heading' && (
            <Reveal
              keyframes={customFade}
              duration={800}
              delay={index * 150}
              triggerOnce
            >
              <Typography variant="h4" component="h3" sx={{ mb: 2, color: 'primary.main' }}>
                {item.content as string}
              </Typography>
            </Reveal>
          )}
          {item.type === 'paragraph' && (
            <Reveal
              keyframes={customFade}
              duration={800}
              delay={index * 150}
              triggerOnce
            >
              <Typography variant="body1" sx={{ mb: 4 }}>
                {item.content as string}
              </Typography>
            </Reveal>
          )}
          {item.type === 'list' && (
            <Box sx={{ mb: 4 }}>
              {(item.content as string[]).map((listItem, i) => (
                <Reveal
                  key={i}
                  keyframes={customFade}
                  duration={600}
                  delay={i * 100}
                  triggerOnce
                >
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ color: 'secondary.main', mr: 1, mt: 0.5 }}>✓</Box>
                    <Typography variant="body1">{listItem}</Typography>
                  </Box>
                </Reveal>
              ))}
            </Box>
          )}
          {item.type === 'italic' && (
            <Reveal
              keyframes={customFade}
              duration={800}
              delay={index * 150}
              triggerOnce
            >
              <Typography variant="body1" sx={{ mb: 4, fontStyle: 'italic' }}>
                {item.content as string}
              </Typography>
            </Reveal>
          )}
          {item.type === 'button' && (
            <Slide direction="up" duration={800} triggerOnce>
              <Button href="#contact-us" variant="contained" color="primary">
                {item.content as string}
              </Button>
            </Slide>
          )}
        </Box>
      ))}
    </>
  );
};

export default AnimatedContent;