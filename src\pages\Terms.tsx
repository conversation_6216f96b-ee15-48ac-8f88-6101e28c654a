
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BinaryBackground from "@/components/BinaryBackground";
import MatrixTrail from "@/components/MatrixTrail";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { FileText, Scale, AlertTriangle, Users } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

const Terms = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [contentRef, contentVisible] = useScrollAnimation(0.1);
  const { t } = useLanguage();

  const intellectualPropertyItems = t('termsPage.intellectualProperty.items') as unknown as string[];
  const clientResponsibilitiesItems = t('termsPage.clientResponsibilities.items') as unknown as string[];
  const limitationsWarrantiesItems = t('termsPage.limitationsWarranties.items') as unknown as string[];
  const paymentTermsItems = t('termsPage.paymentTerms.items') as unknown as string[];

  return (
    <div className="min-h-screen bg-background text-foreground relative">
      <BinaryBackground />
      <MatrixTrail />
      <div className="relative z-20">
        <Header />
        
        <section className="py-24">
          <div className="container mx-auto px-6 max-w-4xl">
            <div 
              ref={headerRef}
              className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
            >
              <h1 className="text-5xl font-bold mb-6 text-gradient">
                {t('termsPage.title')}
              </h1>
              <p className="text-xl text-muted-foreground">
                {t('termsPage.subtitle')}
              </p>
            </div>

            <div 
              ref={contentRef}
              className={`space-y-8 animate-fade-in-up ${contentVisible ? 'visible' : ''}`}
            >
              <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-6 w-6 text-ai-purple" />
                    {t('termsPage.serviceAgreement.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <p>
                    {t('termsPage.serviceAgreement.description')}  
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-blue/10 to-ai-cyan/10 border-ai-blue/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Scale className="h-6 w-6 text-ai-blue" />
                    {t('termsPage.intellectualProperty.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <ul className="space-y-2">
                    {intellectualPropertyItems.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-cyan/10 to-ai-purple/10 border-ai-cyan/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-6 w-6 text-ai-cyan" />
                    {t('termsPage.clientResponsibilities.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <ul className="space-y-2">
                    {clientResponsibilitiesItems.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-violet/10 border-ai-purple/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-6 w-6 text-ai-violet" />
                    {t('termsPage.limitationsWarranties.title')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <p>
                    {t('termsPage.limitationsWarranties.description')}
                  </p>
                  <ul className="space-y-2 mt-4">
                    {limitationsWarrantiesItems.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-pink/10 to-ai-violet/10 border-ai-pink/30">
                <CardHeader>
                  <CardTitle>{t('termsPage.paymentTerms.title')}</CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <ul className="space-y-2">
                    {paymentTermsItems.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-violet/10 to-ai-blue/10 border-ai-violet/30">
                <CardHeader>
                  <CardTitle>{t('termsPage.termination.title')}</CardTitle>
                </CardHeader>
                <CardContent className="prose prose-invert max-w-none">
                  <p>
                    {t('termsPage.termination.description')}
                  </p>
                </CardContent>
              </Card>

              <div className="text-center text-sm text-muted-foreground">
                {t('termsPage.footer')}
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default Terms;
