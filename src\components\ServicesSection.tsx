import { Box, Container, Grid, Typo<PERSON>, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { Fade, Slide } from 'react-awesome-reveal';

const ServicesSection: React.FC = () => {
  const services: { title: string; description: string; items: string[]; image: string }[] = [
    {
      title: 'Photography & Videography',
      description: 'Professional visual content that showcases your products and services in the best light.',
      items: ['Professional product and brand photography', 'Promotional videos and advertising teasers', 'Event and industrial photography'],
      image: '/service-photography.jpg',
    },
    {
      title: 'Graphic Design & Branding',
      description: 'Eye-catching visuals and cohesive brand identity that make your business memorable.',
      items: ['Logo and brand identity design', 'Marketing materials (brochures, posters, social media graphics)', 'Packaging and print design'],
      image: '/service-design.jpg',
    },
    {
      title: 'Digital Marketing & Advertising',
      description: 'Strategic campaigns that increase your online presence and drive targeted traffic.',
      items: ['Social media management & content creation', 'Google & social media ads strategy', 'Website design & development'],
      image: '/service-marketing.jpg',
    },
  ];

  return (
    <Box sx={{ py: 8 }}>
      <Container maxWidth="lg">
        <Fade triggerOnce>
          <Typography variant="h2" component="h2" align="center" sx={{ mb: 2, color: 'primary.main' }}>
            Our Plan for Your Success
          </Typography>
          <Typography variant="h5" align="center" sx={{ mb: 6, maxWidth: '800px', mx: 'auto' }}>
            We provide comprehensive digital marketing solutions tailored to your business goals
          </Typography>
        </Fade>
        <Grid container spacing={4}>
          {services.map((service, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Slide direction="up" triggerOnce delay={index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    boxShadow: 3,
                    transition: 'transform 0.3s ease-in-out',
                    '&:hover': { transform: 'translateY(-8px)' },
                  }}
                >
                  <Box component="img" src={service.image} alt={service.title} sx={{ width: '100%', height: '240px', objectFit: 'cover' }} />
                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    <Typography variant="h5" component="h3" sx={{ mb: 1, color: 'primary.main' }}>
                      {service.title}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {service.description}
                    </Typography>
                    <Box>
                      {service.items.map((item, i) => (
                        <Box key={i} sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                          <Box sx={{ color: 'secondary.main', mr: 1, fontSize: '1.2rem', mt: -0.5 }}>•</Box>
                          <Typography variant="body2">{item}</Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Slide>
            </Grid>
          ))}
        </Grid>
        <Fade triggerOnce delay={600}>
          <Box sx={{ mt: 6, textAlign: 'center' }}>
            <Button href="#contact-us" variant="contained" color="secondary" size="large">
              Let’s Build Your Brand
            </Button>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default ServicesSection;