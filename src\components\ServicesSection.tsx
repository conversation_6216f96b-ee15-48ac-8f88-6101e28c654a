
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";

const services = [
  {
    id: "ai-web-development",
    title: "services.webDev.title",
    description: "services.webDev.description",
    gradient: "from-ai-purple to-ai-violet",
    image: "img/ai-enhanced-web-development.jpg"
  },
  {
    id: "mobile-apps",
    title: "services.mobileDev.title", 
    description: "services.mobileDev.description",
    gradient: "from-ai-violet to-ai-blue",
    image: "img/intelligent-mobile-apps.jpg"
  },
  {
    id: "desktop-software",
    title: "services.desktopDev.title",
    description: "services.desktopDev.description", 
    gradient: "from-ai-blue to-ai-cyan",
    image: "img/smart-desktop-software.jpg"
  },
  {
    id: "cross-platform",
    title: "services.crossPlatform.title",
    description: "services.crossPlatform.description",
    gradient: "from-ai-cyan to-ai-purple",
    image: "img/cross-platform-mastery.jpg"
  },
  {
    id: "ai-system-integration",
    title: "services.systemIntegration.title", 
    description: "services.systemIntegration.description",
    gradient: "from-ai-pink to-ai-violet",
    image: "img/ai-system-integration.jpg"
  },
  {
    id: "ai-optimized-servers",
    title: "services.serverOptimization.title",
    description: "services.serverOptimization.description",
    gradient: "from-ai-violet to-ai-pink",
    image: "img/ai-optimized-servers.jpg"
  },
  {
    id: "firmware-with-ai",
    title: "services.firmwareAI.title",
    description: "services.firmwareAI.description",
    gradient: "from-ai-blue to-ai-purple",
    image: "img/firmware-with-ai.jpg"
  },
  {
    id: "strategic-ai-architecture",
    title: "services.strategicArchitecture.title", 
    description: "services.strategicArchitecture.description",
    gradient: "from-ai-purple to-ai-blue",
    image: "img/strategic-ai-architecture.jpg"
  }
];

const ServicesSection = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [gridRef, gridVisible] = useScrollAnimation(0.1);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.2);
  const { t } = useLanguage();

  return (
    <section id="services" className="py-24 relative">
      <div className="container mx-auto px-6">
        <div 
          ref={headerRef}
          className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
        >
          <h2 className="text-5xl font-bold mb-6 text-gradient text-center">
            {t('services.title')}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto text-center">
            {t('services.subtitle')}
          </p>
        </div>
        
        <div 
          ref={gridRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
        >
          {services.map((service, index) => (
            <Card 
              key={index} 
              className={`group hover:scale-105 transition-all duration-300 bg-card/50 backdrop-blur-sm border-border hover:border-ai-purple/50 hover:glow-box animate-fade-in-up animate-stagger ${gridVisible ? 'visible' : ''}`}
              style={{"--delay": `${index * 0.1}s`} as React.CSSProperties}
            >
              <CardHeader>
                <img src={service.image} alt={t(service.title)} className="w-12 h-12 rounded-lg object-cover mb-4 group-hover:animate-glow-pulse" />
                <CardTitle className="text-lg font-semibold group-hover:text-gradient transition-all duration-300">
                  {t(service.title)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <CardDescription className="pb-4 text-muted-foreground">
                  {t(service.description)}
                </CardDescription>
                {service.id && (
                  <Link to={`/services/${service.id}`}>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="w-full border-ai-purple/30 hover:bg-ai-purple/10 hover:border-ai-purple/50 transition-all duration-300"
                    >
                      {t('common.learnMore')}
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div 
          ref={ctaRef}
          className={`text-center animate-scale-in ${ctaVisible ? 'visible' : ''}`}
        >
          <Link to="/contact">
            <Button 
              size="lg" 
              variant="outline"
              className="border-ai-purple hover:bg-ai-purple/10 px-8 py-4 text-lg"
            >
              {t('services.exploreServices')}
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
