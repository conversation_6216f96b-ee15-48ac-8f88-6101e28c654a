import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLanguage } from '@/contexts/LanguageContext';
import { Calculator, DollarSign, Clock } from 'lucide-react';

// Base hourly rates (USD) reflecting realistic industry standards
const HOURLY_RATES = {
  'Web Development': 80,
  'Mobile App': 90,
  'Desktop Software': 100,
  'Cross-Platform': 95,
  'AI Integration': 120,
  'Server Optimization': 110,
  'Firmware': 115,
  'Software Architecture': 130
};

// Effort multipliers based on project complexity and timeline
const EFFORT_MULTIPLIERS = {
  '1 Month': 1.5,    // Rush projects require more effort, higher cost
  '3 Months': 1.2,
  '6 Months': 1.0,   // Standard pace
  '12 Months': 0.9,  // Longer projects may have efficiencies
  '18+ Months': 0.85
};

// Average hours per month per developer (realistic workload)
const HOURS_PER_MONTH = 160;
// Efficiency gain from expertise (realistic savings)
const COST_SAVINGS_BASE = 0.25; // 25% cost savings due to streamlined processes
const TIME_SAVINGS_BASE = 0.30; // 30% time savings due to experience and tools

interface CostCalculatorModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CostCalculatorModal = ({ isOpen, onClose }: CostCalculatorModalProps) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    projectType: '',
    budget: '',
    timeline: '',
    teamSize: '3' // Default to small team (3 developers)
  });
  const [results, setResults] = useState<{
    estimatedCost: number;
    costSavings: number;
    timeSavings: number;
    industryCost: number;
  } | null>(null);

  const projectTypes = [
    'Web Development',
    'Mobile App',
    'Desktop Software',
    'Cross-Platform',
    'AI Integration',
    'Server Optimization',
    'Firmware',
    'Software Architecture'
  ];

  const timelines = [
    '1 Month',
    '3 Months',
    '6 Months',
    '12 Months',
    '18+ Months'
  ];

  const teamSizes = ['1', '3', '5', '8', '10+'];

  const calculateSavings = () => {
    if (!formData.projectType || !formData.budget || !formData.timeline || !formData.teamSize) return;

    const budget = parseFloat(formData.budget);
    const timelineMonths = formData.timeline === '18+ Months' ? 18 : parseInt(formData.timeline.split(' ')[0]);
    const teamSize = formData.teamSize === '10+' ? 10 : parseInt(formData.teamSize);
    const hourlyRate = HOURLY_RATES[formData.projectType] || 100; // Fallback rate
    const effortMultiplier = EFFORT_MULTIPLIERS[formData.timeline] || 1.0;

    // Estimate industry-standard cost: hours * rate * team size * effort * months
    const totalHours = timelineMonths * HOURS_PER_MONTH * teamSize;
    const industryCost = totalHours * hourlyRate * effortMultiplier;

    // Our estimated cost: assume budget is client's expected spend, adjust for efficiency
    const estimatedCost = budget * (1 - COST_SAVINGS_BASE);
    const costSavings = Math.max(0, industryCost - estimatedCost); // Savings compared to industry
    const timeSavings = timelineMonths * TIME_SAVINGS_BASE;

    setResults({
      estimatedCost: Math.round(estimatedCost),
      costSavings: Math.round(costSavings),
      timeSavings: Math.round(timeSavings * 10) / 10, // Round to 1 decimal
      industryCost: Math.round(industryCost)
    });
  };

  const resetForm = () => {
    setFormData({ projectType: '', budget: '', timeline: '', teamSize: '3' });
    setResults(null);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30">
        <DialogHeader>
          <DialogTitle className="text-2xl text-gradient flex items-center gap-2">
            <Calculator className="h-6 w-6" />
            {t('cost.seeHowMuch')}
          </DialogTitle>
        </DialogHeader>

        {!results ? (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="projectType">{t('calc.projectType')}</Label>
              <Select value={formData.projectType} onValueChange={(value) => setFormData(prev => ({ ...prev, projectType: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select project type" />
                </SelectTrigger>
                <SelectContent>
                  {projectTypes.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="budget">{t('calc.budget')} ($)</Label>
              <Input
                id="budget"
                type="number"
                placeholder="10000"
                value={formData.budget}
                onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeline">{t('calc.timeline')}</Label>
              <Select value={formData.timeline} onValueChange={(value) => setFormData(prev => ({ ...prev, timeline: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timeline" />
                </SelectTrigger>
                <SelectContent>
                  {timelines.map((time) => (
                    <SelectItem key={time} value={time}>{time}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2 mb-3">
              <Label htmlFor="teamSize">{t('calc.teamSize')}</Label>
              <Select value={formData.teamSize} onValueChange={(value) => setFormData(prev => ({ ...prev, teamSize: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select team size" />
                </SelectTrigger>
                <SelectContent>
                  {teamSizes.map((size) => (
                    <SelectItem key={size} value={size}>{size} {size === '10+' ? 'Developers' : 'Developer(s)'}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button
              className="w-full bg-ai-glow hover:scale-105 transition-all duration-300"
              onClick={calculateSavings}
              disabled={!formData.projectType || !formData.budget || !formData.timeline || !formData.teamSize}
            >
              {t('calc.calculate')}
            </Button>
          </div>
        ) : (
          <div className="space-y-6 text-center">
            <div className="text-2xl font-bold text-gradient">
              {t('calc.result')}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 rounded-lg bg-gradient-to-br from-ai-cyan/20 to-ai-blue/20 border border-ai-cyan/30">
                <DollarSign className="h-8 w-8 mx-auto mb-2 text-ai-cyan" />
                <div className="text-2xl font-bold text-ai-cyan">
                  ${results.estimatedCost.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">{t('calc.ourEstimatedCost')}</div>
              </div>

              <div className="p-4 rounded-lg bg-gradient-to-br from-ai-violet/20 to-ai-purple/20 border border-ai-violet/30">
                <DollarSign className="h-8 w-8 mx-auto mb-2 text-ai-violet" />
                <div className="text-2xl font-bold text-ai-violet">
                  ${results.costSavings.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">{t('calc.costSavings')}</div>
              </div>

              <div className="p-4 rounded-lg bg-gradient-to-br from-ai-green/20 to-ai-cyan/20 border border-ai-green/30">
                <Clock className="h-8 w-8 mx-auto mb-2 text-ai-green" />
                <div className="text-2xl font-bold text-ai-green">
                  {results.timeSavings} months
                </div>
                <div className="text-sm text-muted-foreground">{t('calc.timeSavings')}</div>
              </div>

              <div className="p-4 rounded-lg bg-gradient-to-br from-ai-orange/20 to-ai-red/20 border border-ai-orange/30">
                <DollarSign className="h-8 w-8 mx-auto mb-2 text-ai-orange" />
                <div className="text-2xl font-bold text-ai-orange">
                  ${results.industryCost.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">{t('calc.industryAvgCost')}</div>
              </div>
            </div>

            <Link to="/contact">
              <Button
                className="w-full bg-ai-glow hover:scale-105 transition-all duration-300 mb-4 mt-5"
              >
                {t('calc.getFreeQuote')}
              </Button>
            </Link>

            <p className="text-sm text-muted-foreground">
              {t('calc.savingsNote')}
            </p>

            <div className="flex gap-2">
              <Button variant="outline" onClick={resetForm} className="flex-1">
                {t('calc.tryAgain')}
              </Button>
              <Button variant="outline" onClick={onClose} className="flex-1">
                {t('calc.close')}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CostCalculatorModal;