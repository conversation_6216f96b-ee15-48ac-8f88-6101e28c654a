import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";
import { Link } from "react-router-dom";
import { ReactElement, JSXElementConstructor, ReactNode } from "react";

const ContactSection = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [cardRef, cardVisible] = useScrollAnimation(0.1);
  const { t } = useLanguage();

  // Function to format phone number with LTR enforcement
  const formatPhoneNumber = (phone: string | number) => {
    // Ensure LTR rendering with Unicode LRM (Left-to-Right Mark)
    return <span style={{ direction: 'ltr', display: 'inline-block' }}>{phone}</span>;
  };

  return (
    <section id="contact" className="py-24">
      <div className="container mx-auto px-6">
        <div 
          ref={headerRef}
          className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
        >
          <h2 className="text-5xl font-bold mb-6 text-gradient">
            {t('contact.title')}
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
            {t('contact.subtitle')}
          </p>
        </div>
        
        <div className="max-w-2xl mx-auto">
          <Card 
            ref={cardRef}
            className={`bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30 hover:glow-box transition-all duration-300 animate-scale-in ${cardVisible ? 'visible' : ''}`}
          >
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold">{t('common.getInTouch')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="text-center p-4 rounded-lg bg-card/50 backdrop-blur-sm">
                  <CardDescription className="text-lg">
                    <span className="font-semibold text-ai-cyan">{t('contact.email')}:</span><br />
                    {t('contact.emailPlaceholder')}
                  </CardDescription>
                </div>
                <div className="text-center p-4 rounded-lg bg-card/50 backdrop-blur-sm">
                  <CardDescription className={`text-lg text-center`}>
                    <span className="font-semibold text-ai-violet">{t('contact.phone')}:</span><br />
                    {formatPhoneNumber(t('contact.phonePlaceholder'))}
                  </CardDescription>
                </div>
              </div>
              
              <div className="text-center space-y-4">
                <Link to="/contact">
                  <Button
                    variant="outline"
                    size="lg" 
                    className="border-ai-purple hover:bg-ai-purple/10 px-8 py-4 text-lg"
                  >
                    {t('common.contactUs')}
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;