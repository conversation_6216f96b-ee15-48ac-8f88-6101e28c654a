'use client';
import React, { useState } from 'react';
import { Box, Container, Grid, <PERSON><PERSON><PERSON>, <PERSON>ton, Card, CardContent, TextField, MenuItem, Snackbar, Alert, styled } from '@mui/material';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import InstagramIcon from '@mui/icons-material/Instagram';
import { Fade, Slide } from 'react-awesome-reveal';

const SuccessAlert = styled(Alert)(({ theme }) => ({
  backgroundColor: theme.palette.success.main,
  color: theme.palette.success.contrastText,
  '& .MuiAlert-icon': {
    color: theme.palette.success.contrastText,
  },
}));

const ContactSection: React.FC = () => {
  const services: string[] = ['Photography & Videography', 'Graphic Design & Branding', 'Digital Marketing & Advertising', 'Website Development', 'Social Media Management', 'Other'];
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    service: '',
    message: '',
  });
  const [errors, setErrors] = useState({
    name: '',
    email: '',
    service: '',
    message: '',
  });
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '', // Clear error on change
    }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    let isValid = true;
    const newErrors = { name: '', email: '', service: '', message: '' };

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      isValid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
      isValid = false;
    }

    if (!formData.service) {
      newErrors.service = 'Please select a service';
      isValid = false;
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
      isValid = false;
    }

    setErrors(newErrors);

    if (isValid) {
      try {
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData),
        });

        if (response.ok) {
          setFormData({ name: '', email: '', service: '', message: '' });
          setSnackbarOpen(true);
        } else {
          const data = await response.json();
          alert(data.message || 'Something went wrong.');
        }
      } catch {
        alert('Error sending message. Please try again later.');
      }
    }
  };

  const handleCloseSnackbar = (event: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  return (
    <Box sx={{ py: 8, bgcolor: 'background.paper' }} id="contact-us">
      <Container maxWidth="lg">
        <Fade triggerOnce>
          <Typography variant="h2" component="h2" align="center" sx={{ mb: 6, color: 'primary.main' }}>
            Meet Your Digital Marketing Partners in Nova Scotia
          </Typography>
        </Fade>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Slide direction="left" triggerOnce>
              <Typography variant="h4" component="h3" sx={{ mb: 3, color: 'primary.main' }}>
                Get in Touch
              </Typography>
              <Box sx={{ mb: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <LocationOnIcon sx={{ color: 'primary.main', mr: 1 }} />
                  <Typography variant="body1">Address: Bedford, NS</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PhoneIcon sx={{ color: 'primary.main', mr: 1 }} />
                  <Typography 
                    variant="body1" 
                    component="a" 
                    href="tel:************" 
                    sx={{ 
                      color: 'inherit', 
                      textDecoration: 'none', 
                      '&:hover': { textDecoration: 'underline' } 
                    }}
                  >
                    Phone: ************
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <EmailIcon sx={{ color: 'primary.main', mr: 1 }} />
                  <Typography 
                    variant="body1" 
                    component="a" 
                    href="mailto:<EMAIL>" 
                    sx={{ 
                      color: 'inherit', 
                      textDecoration: 'none', 
                      '&:hover': { textDecoration: 'underline' } 
                    }}
                  >
                    Email: <EMAIL>
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <InstagramIcon sx={{ color: 'primary.main', mr: 1 }} />
                  <Typography 
                    variant="body1" 
                    component="a" 
                    href="https://www.instagram.com/benza.ad" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    sx={{ 
                      color: 'inherit', 
                      textDecoration: 'none', 
                      '&:hover': { textDecoration: 'underline' } 
                    }}
                  >
                    Instagram: @benza.ad
                  </Typography>
                </Box>
              </Box>
              <Box
                component="img"
                src="/team-photo.png"
                alt="Benza Advertising Team"
                sx={{ width: '100%', height: 'auto', borderRadius: 2, boxShadow: 3 }}
              />
            </Slide>
          </Grid>
          <Grid item xs={12} md={6}>
            <Fade triggerOnce>
              <Card sx={{ p: 3, boxShadow: 3 }}>
                <CardContent>
                  <Typography variant="h4" component="h3" sx={{ mb: 3, color: 'primary.main' }}>
                    Contact Us
                  </Typography>
                  <Box component="form" onSubmit={handleSubmit} sx={{ '& .MuiTextField-root': { mb: 3 } }}>
                    <TextField
                      fullWidth
                      label="Your Name"
                      variant="outlined"
                      required
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      error={!!errors.name}
                      helperText={errors.name}
                    />
                    <TextField
                      fullWidth
                      label="Email Address"
                      variant="outlined"
                      type="email"
                      required
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      error={!!errors.email}
                      helperText={errors.email}
                    />
                    <TextField
                      fullWidth
                      select
                      label="Service Interested In"
                      variant="outlined"
                      value={formData.service}
                      onChange={handleChange}
                      name="service"
                      required
                      error={!!errors.service}
                      helperText={errors.service}
                    >
                      <MenuItem value="">
                        <em>Select a service</em>
                      </MenuItem>
                      {services.map((service) => (
                        <MenuItem key={service} value={service}>
                          {service}
                        </MenuItem>
                      ))}
                    </TextField>
                    <TextField
                      fullWidth
                      label="Your Message"
                      variant="outlined"
                      multiline
                      rows={4}
                      required
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      error={!!errors.message}
                      helperText={errors.message}
                    />
                    <Button fullWidth variant="contained" color="primary" size="large" sx={{ py: 1.5 }} type="submit">
                      Send Message
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Fade>
          </Grid>
        </Grid>
      </Container>
      <Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={handleCloseSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
        <SuccessAlert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          Message has been sent!
        </SuccessAlert>
      </Snackbar>
    </Box>
  );
};

export default ContactSection;