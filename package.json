{"name": "benza-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start -p 3085", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "next": "15.2.3", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-awesome-reveal": "^4.3.1", "react-dom": "^19.0.0", "react-player": "^2.16.0", "styled-components": "^6.1.16", "stylis-plugin-rtl": "^2.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/styled-components": "^5.1.34", "eslint": "^9", "eslint-config-next": "15.2.3", "typescript": "^5"}}