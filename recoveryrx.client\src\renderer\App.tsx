import { useEffect, useMemo, useState } from 'react';
import './App.css';
import { useStatistics } from './useStatistics';
import { LanguageProvider } from './i18n/LanguageContext';
import { useTranslation } from 'react-i18next';

// Initialize i18n
import './i18n/i18n';

// Import Auth context and Login Page
import { AuthProvider, useAuth } from './context/AuthContext'; 
import { LoginPage } from './pages/LoginPage'; 

// Import layout components
import { MainLayout } from './layout/MainLayout';
// Import electron bridge
import { electronBridge, type View, type StaticData } from './utils/electronBridge';

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </LanguageProvider>
  );
}

// New component to access AuthContext after AuthProvider is set up
function AppContent() {
  const { isAuthenticated, isLoading } = useAuth();
  const staticData = useStaticData();
  const statistics = useStatistics(10);
  const [activeView, setActiveView] = useState<View>('CPU');
  const cpuUsages = useMemo(
    () => statistics.map((stat) => stat.cpuUsage),
    [statistics]
  );
  const ramUsages = useMemo(
    () => statistics.map((stat) => stat.ramUsage),
    [statistics]
  );
  const storageUsages = useMemo(
    () => statistics.map((stat) => stat.storageUsage),
    [statistics]
  );
  // Calculate active usages based on the selected view
  // This can be used for chart visualization in the future
  // const _activeUsages = useMemo(() => {
  //   switch (activeView) {
  //     case 'CPU':
  //       return cpuUsages;
  //     case 'RAM':
  //       return ramUsages;
  //     case 'STORAGE':
  //       return storageUsages;
  //   }
  // }, [activeView, cpuUsages, ramUsages, storageUsages]);

  useEffect(() => {
    return electronBridge.subscribeChangeView((view) => setActiveView(view));
  }, []);

  const { t } = useTranslation();

  if (isLoading) {
    // You can replace this with a more sophisticated loading spinner/component
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  // If authenticated, show the main application content
  return (
    <div className="App">
      <MainLayout>
      <div className="main-layout">
          
          
          <div className="window-content">
            <h1 className="text-3xl font-bold mb-6">
              {t('app.hello')}
            </h1>
          
            <div className="grid grid-cols-3 gap-4 mb-8">
              <SelectOption
                onClick={() => setActiveView('CPU')}
                title="CPU"
                view="CPU"
                subTitle={staticData?.cpuModel ?? ''}
                data={cpuUsages}
              />
              <SelectOption
                onClick={() => setActiveView('RAM')}
                title="RAM"
                view="RAM"
                subTitle={(staticData?.totalMemoryGB.toString() ?? '') + ' GB'}
                data={ramUsages}
              />
              <SelectOption
                onClick={() => setActiveView('STORAGE')}
                title="STORAGE"
                view="STORAGE"
                subTitle={(staticData?.totalStorage.toString() ?? '') + ' GB'}
                data={storageUsages}
              />
            </div>
          
            <div className="p-4 border rounded-lg bg-card">
              <h2 className="text-xl font-semibold mb-4">{activeView} {t('app.statistics')}</h2>
              <div className="h-64 bg-muted/20 rounded-md flex items-center justify-center">
                {/* Placeholder for charts/graphs */}
                <p className="text-muted-foreground">{t('app.chartVisualization')}</p>
              </div>
            </div>
          </div>
        </div>
        </MainLayout>
      </div>
  );
}


function SelectOption(props: {
  title: string;
  view: View;
  subTitle: string;
  data: number[];
  onClick: () => void;
}) {
  return (
    <button 
      className="p-4 border rounded-lg bg-card hover:bg-accent/10 transition-colors"
      onClick={props.onClick}
    >
      <div className="flex flex-col items-center text-center">
        <h3 className="text-lg font-medium">{props.title}</h3>
        <p className="text-sm text-muted-foreground">{props.subTitle}</p>
        <div className="mt-2 w-full h-16 bg-muted/20 rounded-md">
          {/* Placeholder for mini chart */}
        </div>
      </div>
    </button>
  );
}


function useStaticData() {
  const [staticData, setStaticData] = useState<StaticData | null>(null);

  useEffect(() => {
    (async () => {
      // Ensure electronBridge is available before calling
      if (window.electronBridge) {
        setStaticData(await electronBridge.getStaticData());
      } else {
        console.warn('electronBridge not available when trying to getStaticData');
      }
    })();
  }, []);

  return staticData;
}

export default App;
