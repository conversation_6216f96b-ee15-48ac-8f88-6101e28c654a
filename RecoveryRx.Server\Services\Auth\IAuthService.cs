using RecoveryRx.Server.DTOs.Auth;
using System;
using System.Threading.Tasks;

namespace RecoveryRx.Server.Services.Auth
{
    public interface IAuthService
    {
        Task<AuthResponseDto> RegisterAsync(RegisterRequestDto registerDto, string ipAddress, string userAgent);
        Task<AuthResponseDto> LoginAsync(LoginRequestDto loginDto, string ipAddress, string userAgent);
        Task<bool> ValidateTokenAsync(string token);
        Task<AuthResponseDto> GetCurrentUserAsync(Guid userId);
    }
}
