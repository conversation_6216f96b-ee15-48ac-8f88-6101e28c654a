{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Urls": "http://localhost:5000;https://localhost:5001", "DatabaseSettings": {"Provider": "Sqlite", "ConnectionStrings": {"SQLite": "Data Source=recovery_rx_db.db", "MySQL": "Server=localhost;Database=recovery_rx_db;Uid=root;Pwd=********;Port=3306;"}}, "Jwt": {"Key": "REPLACE_THIS_WITH_A_VERY_STRONG_AND_SECRET_KEY_MIN_32_CHARS", "Issuer": "RecoveryRx.Server", "Audience": "RecoveryRx.Client", "ExpirationInMinutes": 60}}