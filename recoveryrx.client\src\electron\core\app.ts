import { app} from 'electron';
import { closeSplashScreen, createSplashScreen } from '../ui/splash.js';
import { getStaticData, pollResources } from '../services/resourceManager.js';
import { ipcMainHandle, ipcMainOn } from '../ipc/handlers.js';
import { createTray } from '../ui/tray.js';
import { createMenu } from '../ui/menu.js';
import { 
    createMainWindow, 
    setupWindowCloseHandlers, 
    handleMinimizeToTrayAction, 
    handleCloseAction 
} from './window.js';

/**
 * Initialize the application
 */
export function initializeApp(): void {
    app.on('ready', () => {
        // Create and show splash screen
        const splash = createSplashScreen();
        // Create main application window
        const mainWindow = createMainWindow();
       
        // Once the main window is ready, close the splash screen
        mainWindow.webContents.on('did-finish-load', () => {
            // Allow a small delay to make the transition smoother
            setTimeout(() => {
                mainWindow.show();
                closeSplashScreen();
            }, 1000);
        });

        // Hide the main window until splash screen is done
        mainWindow.hide();

        pollResources(mainWindow);

        ipcMainHandle('getStaticData', () => {
            return getStaticData();
        });

        ipcMainHandle('isWindowMaximized', () => {
            return mainWindow.isMaximized();
        });

        ipcMainOn('sendFrameAction', (payload: string) => {
            switch (payload) {
                case 'CLOSE':
                    handleCloseAction();
                    break;
                case 'MAXIMIZE':
                    mainWindow.maximize();
                    break;
                case 'UNMAXIMIZE':
                    mainWindow.unmaximize();
                    break;
                case 'MINIMIZE':
                    mainWindow.minimize();
                    break;
                case 'MINIMIZE_TO_TRAY':
                    handleMinimizeToTrayAction();
                    break;
                default:
                    console.warn(`Unhandled frame action: ${payload}`);
                    break;
            }
        });

        // Send maximize/unmaximize events to the renderer
        mainWindow.on('maximize', () => {
            mainWindow.webContents.send('window-state-change', true);
        });

        mainWindow.on('unmaximize', () => {
            mainWindow.webContents.send('window-state-change', false);
        });

        createTray(mainWindow);
        setupWindowCloseHandlers(mainWindow);
        createMenu(mainWindow);
    });
}
