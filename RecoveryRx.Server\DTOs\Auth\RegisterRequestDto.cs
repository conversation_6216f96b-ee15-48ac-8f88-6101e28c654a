using System.ComponentModel.DataAnnotations;

namespace RecoveryRx.Server.DTOs.Auth
{
    public class RegisterRequestDto
    {
        [Required]
        [StringLength(256, MinimumLength = 3)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(256)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [Compare("Password")]
        public string ConfirmPassword { get; set; } = string.Empty;

        public string? FirstName { get; set; }
        
        public string? LastName { get; set; }
    }
}
