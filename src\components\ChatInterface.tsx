
import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, X } from 'lucide-react';
import { UserInfo } from './UserInfoPanel';

interface ChatInterfaceProps {
  userInfo: UserInfo;
  onClose: () => void;
}

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'maxqit';
  timestamp: Date;
}

const ChatInterface = ({ userInfo, onClose }: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: `Hello ${userInfo.name}! Welcome to Maxqit. How can we help you today?`,
      sender: 'maxqit',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = () => {
    if (inputValue.trim()) {
      const newMessage: Message = {
        id: Date.now().toString(),
        text: inputValue.trim(),
        sender: 'user',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, newMessage]);
      setInputValue('');
      setIsTyping(true);

      // Simulate AI response
      setTimeout(() => {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: "Thanks for your message! Our team will get back to you shortly with a detailed response.",
          sender: 'maxqit',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsTyping(false);
      }, 2000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  return (
    <div className="fixed bottom-6 right-6 w-96 h-96 bg-background border border-border rounded-lg shadow-xl z-50 flex flex-col animate-slide-in-up">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-gradient-to-r from-ai-purple/10 to-ai-cyan/10">
        <h3 className="text-lg font-bold text-gradient">Chat with Maxqit</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0 hover:bg-destructive/20"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 bg-gradient-to-b from-background to-card/50">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} animate-fade-in`}
          >
            <div
              className={`max-w-xs px-3 py-2 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-gradient-to-r from-ai-cyan to-ai-blue text-white'
                  : 'bg-muted text-foreground border border-border'
              }`}
            >
              <p className="text-sm">{message.text}</p>
              <span className="text-xs opacity-70">
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div className="flex justify-start animate-fade-in">
            <div className="bg-muted px-3 py-2 rounded-lg border border-border">
              <p className="text-sm text-muted-foreground">Maxqit is typing...</p>
              <div className="flex space-x-1 mt-1">
                <div className="w-2 h-2 bg-ai-cyan rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-ai-purple rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-ai-blue rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-border bg-card/50">
        <div className="flex space-x-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message…"
            className="flex-1 bg-background border-ai-purple/30 focus:border-ai-cyan"
          />
          <Button
            onClick={sendMessage}
            size="sm"
            className="bg-gradient-to-r from-ai-cyan to-ai-purple hover:opacity-90 p-2"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* AI decoration elements */}
      <div className="absolute top-4 left-4 w-1 h-1 bg-ai-cyan rounded-full opacity-50 animate-pulse"></div>
      <div className="absolute bottom-4 right-4 w-1 h-1 bg-ai-purple rounded-full opacity-60 animate-pulse"></div>
    </div>
  );
};

export default ChatInterface;
