import { useLanguage } from "@/contexts/LanguageContext";
import { Link } from "react-router-dom";

const Footer = () => {
  const { t } = useLanguage();

  return (
    <footer className="py-12 border-t border-border bg-card/20 backdrop-blur-sm">
      <div className="container mx-auto px-6">
        <div className="text-center">
          <div className="flex justify-center items-center mb-6">
            <Link to="/" className="flex items-center justify-center">
              <img 
                src="/img/logo-light.svg" 
                alt="Maxqit" 
                className="h-8 w-auto mx-auto"
              />
            </Link>
          </div>
          <p className="text-muted-foreground mb-6">
            {t('footer.description')}
          </p>
          <div className="flex justify-center gap-8">
            <Link to="/privacy" className="text-muted-foreground hover:text-ai-purple transition-colors">
              {t('footer.privacy')}
            </Link>
            <Link to="/terms" className="text-muted-foreground hover:text-ai-purple transition-colors">
              {t('footer.terms')}
            </Link>
            <Link to="/support" className="text-muted-foreground hover:text-ai-purple transition-colors">
              {t('footer.support')}
            </Link>
          </div>
          <div className="mt-6 text-sm text-muted-foreground">
            {t('footer.rights')} @ {new Date().getFullYear()} {t('footer.company')}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;