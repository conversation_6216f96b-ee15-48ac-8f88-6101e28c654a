
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BinaryBackground from "@/components/BinaryBackground";
import MatrixTrail from "@/components/MatrixTrail";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { MessageCircle, Mail, Phone, Clock } from "lucide-react";
import { Link } from "react-router-dom";
import { useLanguage } from "@/contexts/LanguageContext";

const Support = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [contentRef, contentVisible] = useScrollAnimation(0.1);
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-background text-foreground relative">
      <BinaryBackground />
      <MatrixTrail />
      <div className="relative z-20">
        <Header />
        
        <section className="py-24">
          <div className="container mx-auto px-6">
            <div 
              ref={headerRef}
              className={`text-center mb-16 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
            >
              <h1 className="text-5xl font-bold mb-6 text-gradient">
                {t('supportPage.title')}
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {t('supportPage.subtitle')}
              </p>
            </div>

            <div 
              ref={contentRef}
              className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16 animate-fade-in-up ${contentVisible ? 'visible' : ''}`}
            >
              <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-violet/10 border-ai-purple/30 hover:glow-box transition-all duration-300">
                <CardHeader className="text-center">
                  <Mail className="h-12 w-12 mx-auto mb-4 text-ai-purple" />
                  <CardTitle>{t('supportPage.emailSupport.title')}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <CardDescription>
                    {t('supportPage.emailSupport.description')}
                  </CardDescription>
                  <Button className="w-full bg-ai-glow hover:scale-105 transition-all duration-300">
                    <Mail className="h-4 w-4 mr-2" />
                    {t('supportPage.emailSupport.button')}
                  </Button>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-blue/10 to-ai-cyan/10 border-ai-blue/30 hover:glow-box transition-all duration-300">
                <CardHeader className="text-center">
                  <Phone className="h-12 w-12 mx-auto mb-4 text-ai-blue" />
                  <CardTitle>{t('supportPage.phoneSupport.title')}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <CardDescription>
                    {t('supportPage.phoneSupport.description')}
                  </CardDescription>
                  <Button className="w-full bg-ai-glow hover:scale-105 transition-all duration-300">
                    <Phone className="h-4 w-4 mr-2" />
                    {t('supportPage.phoneSupport.button')}
                  </Button>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-cyan/10 to-ai-purple/10 border-ai-cyan/30 hover:glow-box transition-all duration-300">
                <CardHeader className="text-center">
                  <MessageCircle className="h-12 w-12 mx-auto mb-4 text-ai-cyan" />
                  <CardTitle>{t('supportPage.liveChat.title')}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <CardDescription>
                    {t('supportPage.liveChat.description')}
                  </CardDescription>
                  <Button className="w-full bg-ai-glow hover:scale-105 transition-all duration-300">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    {t('supportPage.liveChat.button')}
                  </Button>
                </CardContent>
              </Card>
            </div>

            <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30 max-w-4xl mx-auto">
              <CardHeader>
                <CardTitle className="text-2xl text-center flex items-center justify-center gap-2">
                  <Clock className="h-6 w-6 text-ai-purple" />
                  {t('supportPage.supportHours.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div>
                    <h3 className="font-semibold text-ai-cyan mb-2">{t('supportPage.supportHours.email.title')}</h3>
                    <p className="text-muted-foreground">{t('supportPage.supportHours.email.availability')}</p>
                    <p className="text-sm text-ai-purple">{t('supportPage.supportHours.email.response')}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-ai-cyan mb-2">{t('supportPage.supportHours.phone.title')}</h3>
                    <p className="text-muted-foreground">{t('supportPage.supportHours.phone.availability')}</p>
                    <p className="text-sm text-ai-purple">{t('supportPage.supportHours.phone.response')}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-ai-cyan mb-2">{t('supportPage.supportHours.emergency.title')}</h3>
                    <p className="text-muted-foreground">{t('supportPage.supportHours.emergency.availability')}</p>
                    <p className="text-sm text-ai-purple">{t('supportPage.supportHours.emergency.response')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="text-center mt-12">
              <Link to="/contact">
                <Button 
                  size="lg"
                  className="bg-ai-glow hover:scale-105 transition-all duration-300 px-8 py-4 text-lg animate-glow-pulse"
                >
                  {t('supportPage.contactSupport')}
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default Support;
