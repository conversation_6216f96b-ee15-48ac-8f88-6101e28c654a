using RecoveryRx.Server.DTOs;

namespace RecoveryRx.Server.Services;

public interface IPatientService
{
    Task<IEnumerable<PatientDto>> GetAllPatientsAsync();
    Task<PatientDto?> GetPatientByIdAsync(long id);
    Task<PatientDto> CreatePatientAsync(CreateUpdatePatientDto createPatientDto);
    Task<PatientDto?> UpdatePatientAsync(long id, CreateUpdatePatientDto updatePatientDto);
    Task<bool> DeletePatientAsync(long id);
}
