@RecoveryRx.Server_HostAddress = http://localhost:5000

#######################
### Database Controller
#######################

### Test database connection
GET {{RecoveryRx.Server_HostAddress}}/api/database/test-connection
Accept: application/json

### Get current database provider
GET {{RecoveryRx.Server_HostAddress}}/api/database/provider
Accept: application/json

### Initialize database
POST {{RecoveryRx.Server_HostAddress}}/api/database/initialize
Accept: application/json

### Apply database migrations
POST {{RecoveryRx.Server_HostAddress}}/api/database/apply-migrations
Accept: application/json

### Configure database provider (SQLite)
POST {{RecoveryRx.Server_HostAddress}}/api/database/configure-provider
Content-Type: application/json

{
  "provider": "SQLite",
  "sqliteSettings": {
    "databasePath": "Data/RecoveryRx.db"
  }
}

### Configure database provider (MySQL)
POST {{RecoveryRx.Server_HostAddress}}/api/database/configure-provider
Content-Type: application/json

{
  "provider": "MySQL",
  "mySqlSettings": {
    "server": "localhost",
    "port": 3306,
    "database": "recoveryrx",
    "userId": "root",
    "password": "your_password"
  }
}

#######################
### Patient Controller
#######################

### Get all patients
GET {{RecoveryRx.Server_HostAddress}}/api/patient
Accept: application/json

### Get patient by ID
GET {{RecoveryRx.Server_HostAddress}}/api/patient/1
Accept: application/json

### Create new patient
POST {{RecoveryRx.Server_HostAddress}}/api/patient
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "dateOfBirth": "1980-01-01",
  "gender": 0,
  "email": "<EMAIL>",
  "phoneNumber": "************",
  "address": "123 Main St, Anytown, USA"
}

### Update patient
PUT {{RecoveryRx.Server_HostAddress}}/api/patient/1
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "dateOfBirth": "1980-01-01",
  "gender": 0,
  "email": "<EMAIL>",
  "phoneNumber": "************",
  "address": "456 Oak St, Anytown, USA"
}

### Delete patient
DELETE {{RecoveryRx.Server_HostAddress}}/api/patient/1

#######################
### Users Controller
#######################

### Get all users
GET {{RecoveryRx.Server_HostAddress}}/api/users
Accept: application/json

### Get user by ID
GET {{RecoveryRx.Server_HostAddress}}/api/users/1
Accept: application/json

### Get user by username
GET {{RecoveryRx.Server_HostAddress}}/api/users/username/admin
Accept: application/json

### Create new user
POST {{RecoveryRx.Server_HostAddress}}/api/users
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "Password123!",
  "firstName": "New",
  "lastName": "User"
}

### Update user
PUT {{RecoveryRx.Server_HostAddress}}/api/users/1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "NewPassword123!",
  "firstName": "Updated",
  "lastName": "User"
}

### Delete user
DELETE {{RecoveryRx.Server_HostAddress}}/api/users/1

### Assign role to user
POST {{RecoveryRx.Server_HostAddress}}/api/users/assign-role
Content-Type: application/json

{
  "userId": 1,
  "roleId": 2
}

### Remove role from user
POST {{RecoveryRx.Server_HostAddress}}/api/users/remove-role
Content-Type: application/json

{
  "userId": 1,
  "roleId": 2
}

#######################
### Roles Controller
#######################

### Get all roles
GET {{RecoveryRx.Server_HostAddress}}/api/roles
Accept: application/json

### Get role by ID
GET {{RecoveryRx.Server_HostAddress}}/api/roles/1
Accept: application/json

### Get role by name
GET {{RecoveryRx.Server_HostAddress}}/api/roles/name/Admin
Accept: application/json

### Create new role
POST {{RecoveryRx.Server_HostAddress}}/api/roles
Content-Type: application/json

{
  "name": "Therapist",
  "description": "Can manage patients and treatments"
}

### Update role
PUT {{RecoveryRx.Server_HostAddress}}/api/roles/1
Content-Type: application/json

{
  "name": "Administrator",
  "description": "System administrator with full access"
}

### Delete role
DELETE {{RecoveryRx.Server_HostAddress}}/api/roles/1

#######################
### Localization Test Controller
#######################

### Get localized gender values (English)
GET {{RecoveryRx.Server_HostAddress}}/api/localizationtest/gender-values
Accept: application/json
Accept-Language: en-US

### Get localized gender values (Spanish)
GET {{RecoveryRx.Server_HostAddress}}/api/localizationtest/gender-values
Accept: application/json
Accept-Language: es-ES
