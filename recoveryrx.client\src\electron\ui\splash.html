<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>RecoveryRx</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      background-color: transparent;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      overflow: hidden;
      color: #333;
    }
    
    .splash-container {
      background-color: #f5f5f5;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 20px;
      text-align: center;
      width: 300px;
      animation: fadeIn 0.5s ease-in-out;
    }
    
    .logo {
      width: 150px;
      height: auto;
      margin-bottom: 20px;
    }
    
    .app-name {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .version {
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
    }
    
    .loading {
      display: inline-block;
      position: relative;
      width: 80px;
      height: 10px;
    }
    
    .loading div {
      position: absolute;
      top: 0;
      width: 13px;
      height: 13px;
      border-radius: 50%;
      background: #3498db;
      animation-timing-function: cubic-bezier(0, 1, 1, 0);
    }
    
    .loading div:nth-child(1) {
      left: 8px;
      animation: loading1 0.6s infinite;
    }
    
    .loading div:nth-child(2) {
      left: 8px;
      animation: loading2 0.6s infinite;
    }
    
    .loading div:nth-child(3) {
      left: 32px;
      animation: loading2 0.6s infinite;
    }
    
    .loading div:nth-child(4) {
      left: 56px;
      animation: loading3 0.6s infinite;
    }
    
    @keyframes loading1 {
      0% {
        transform: scale(0);
      }
      100% {
        transform: scale(1);
      }
    }
    
    @keyframes loading3 {
      0% {
        transform: scale(1);
      }
      100% {
        transform: scale(0);
      }
    }
    
    @keyframes loading2 {
      0% {
        transform: translate(0, 0);
      }
      100% {
        transform: translate(24px, 0);
      }
    }
    
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>
<body>
  <div class="splash-container">
    <!-- Fallback to a simple colored div if image doesn't load -->
    <div style="width: 150px; height: 150px; background-color: #3498db; border-radius: 10px; margin-bottom: 20px;" id="logo-fallback"></div>
    <img src="" alt="RecoveryRx Logo" class="logo" id="app-logo" style="display: none;">
    <div class="app-name">RecoveryRx</div>
    <div class="version" id="app-version">Version: Loading...</div>
    <div class="loading">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>
  </div>

  <script>
    // Listen for the app version from the main process
    window.addEventListener('DOMContentLoaded', () => {
      // Set a default version
      document.getElementById('app-version').textContent = `Version: ${window.location.hash.substring(1) || '0.0.0'}`;
    });
  </script>
</body>
</html>
