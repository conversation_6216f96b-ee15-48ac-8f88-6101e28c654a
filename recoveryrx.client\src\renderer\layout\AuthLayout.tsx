import React from 'react';
import { WindowHeader } from '../components/WindowHeader';
import { useLanguage } from '../i18n/LanguageContext';
import { LanguageSwitcher } from '../components/LanguageSwitcher';

interface AuthLayoutProps {
  children: React.ReactNode;
}

export function AuthLayout({ children }: AuthLayoutProps) {
  const { isRTL } = useLanguage();

  // Apply RTL to document body when language changes
  React.useEffect(() => {
    if (isRTL) {
      document.documentElement.dir = 'rtl';
      document.body.classList.add('rtl'); // Removed 'lang-fa' as it might be specific to MainLayout
    } else {
      document.documentElement.dir = 'ltr';
      document.body.classList.remove('rtl');
    }
    // Cleanup function to remove RTL from body when component unmounts or isRTL changes
    return () => {
      document.documentElement.dir = 'ltr'; // Default to LTR
      document.body.classList.remove('rtl');
    };
  }, [isRTL]);

  return (
    <div className={`flex flex-col h-screen ${isRTL ? 'rtl' : ''}`}>
      <WindowHeader>
        <div className="flex items-center">
          <LanguageSwitcher />
        </div>
      </WindowHeader>
      <div className="auth-page-container flex-1 overflow-auto">
        {/* Content of the auth page (e.g., LoginPage) will be rendered here */}
        {children}
      </div>
    </div>
  );
}
