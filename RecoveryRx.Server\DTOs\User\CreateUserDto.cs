using System.ComponentModel.DataAnnotations;

namespace RecoveryRx.Server.DTOs.User
{
    public class CreateUserDto
    {
        [Required]
        [MaxLength(256)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [MaxLength(256)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MinLength(8)] // Example: Enforce a minimum password length
        public string Password { get; set; } = string.Empty;

        public string? FirstName { get; set; }
        public string? LastName { get; set; }
    }
}
