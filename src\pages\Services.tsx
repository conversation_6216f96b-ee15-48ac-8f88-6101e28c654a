import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, Check } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BinaryBackground from "@/components/BinaryBackground";
import MatrixTrail from "@/components/MatrixTrail";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";




const Services = () => {
  const { serviceId } = useParams();
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [contentRef, contentVisible] = useScrollAnimation(0.1);
  const [featuresRef, featuresVisible] = useScrollAnimation(0.2);
  const { t } = useLanguage();

  const servicesData = [
    {
      id: "ai-web-development",
      title: t('servicePages.aiWebDevelopment.title'),
      description: t('servicePages.aiWebDevelopment.description'),
      fullDescription: t('servicePages.aiWebDevelopment.fullDescription'),
      features: t('servicePages.aiWebDevelopment.features') as unknown as string[],
      technologies: ["React", "Next.js", "Svelte", "Node.js", "TypeScript", "WebAssembly"],
      gradient: "from-ai-purple to-ai-violet",
      image: "/img/ai-enhanced-web-development.jpg"
    },
    {
      id: "mobile-apps",
      title: t('servicePages.mobileApps.title'),
      description: t('servicePages.mobileApps.description'),
      fullDescription: t('servicePages.mobileApps.fullDescription'),
      features: t('servicePages.mobileApps.features') as unknown as string[],
      technologies: ["Kotlin", "React Native", "Flutter", "Swift", "TensorFlow Lite", "Core ML", "TypeScript"],
      gradient: "from-ai-violet to-ai-blue",
      image: "/img/intelligent-mobile-apps.jpg"
    },
    {
      id: "desktop-software",
      title: t('servicePages.desktopSoftware.title'),
      description: t('servicePages.desktopSoftware.description'),
      fullDescription: t('servicePages.desktopSoftware.fullDescription'),
      features: t('servicePages.desktopSoftware.features') as unknown as string[],
      technologies: ["Kotlin(JVM)", "Electron", "Qt", "WPF", ".NET Core", "Rust", "C++", "PyQt"],
      gradient: "from-ai-blue to-ai-cyan",
      image: "/img/smart-desktop-software.jpg"
    },
    {
      id: "cross-platform",
      title: t('servicePages.crossPlatform.title'),
      description: t('servicePages.crossPlatform.description'),
      fullDescription: t('servicePages.crossPlatform.fullDescription'),
      features: t('servicePages.crossPlatform.features') as unknown as string[],
      technologies: ["Kotlin(Multiplatform)", "React Native", "Flutter", "Tauri", "Capacitor", "WebAssembly"],
      gradient: "from-ai-cyan to-ai-purple",
      image: "img/cross-platform-mastery.jpg"
    },
    // --- New Services Added Below ---
    {
      id: "ai-system-integration",
      title: t('servicePages.aiSystemIntegration.title'),
      description: t('servicePages.aiSystemIntegration.description'),
      fullDescription: t('servicePages.aiSystemIntegration.fullDescription'),
      features: t('servicePages.aiSystemIntegration.features') as unknown as string[],
      technologies: ["React", "Next.js", "Node.js", "TensorFlow.js", "Hugging Face Transformers", "AWS Amplify", "Serverless Framework"],
      gradient: "from-ai-pink to-ai-violet",
      image: "/img/ai-system-integration.jpg"
    },
    {
      id: "ai-optimized-servers",
      title: t('servicePages.aiOptimizedServers.title'),
      description: t('servicePages.aiOptimizedServers.description'),
      fullDescription: t('servicePages.aiOptimizedServers.fullDescription'),
      features: t('servicePages.aiOptimizedServers.features') as unknown as string[],
      technologies: ["Docker", "Kubernetes", "Jenkins", "GitLab CI", "Prometheus", "Grafana", "Terraform", "Github Actions"],
      gradient: "from-ai-violet to-ai-pink",
      image: "/img/ai-optimized-servers.jpg"
    },
    {
      id: "firmware-with-ai",
      title: t('servicePages.firmwareAI.title'),
      description: t('servicePages.firmwareAI.description'),
      fullDescription: t('servicePages.firmwareAI.fullDescription'),
      features: t('servicePages.firmwareAI.features') as unknown as string[],
      technologies: ["C", "C++", "Embedded Linux", "Zephyr RTOS", "PlatformIO", "Arduino", "TensorFlow Lite Micro"],
      gradient: "from-ai-blue to-ai-purple",
      image: "/img/firmware-with-ai.jpg"
    },
    {
      id: "strategic-ai-architecture",
      title: t('servicePages.strategicAIArchitecture.title'),
      description: t('servicePages.strategicAIArchitecture.description'),
      fullDescription: t('servicePages.strategicAIArchitecture.fullDescription'),
      features: t('servicePages.strategicAIArchitecture.features') as unknown as string[],
      technologies: ["PostgreSQL", "MongoDB", "GraphQL", "OpenAPI", "Terraform", "Draw.io", "Lucidchart"],
      gradient: "from-ai-purple to-ai-blue",
      image: "/img/strategic-ai-architecture.jpg"
    }
  ];

  const service = servicesData.find(s => s.id === serviceId);

  if (!service) {
    return (
      <div className="min-h-screen bg-background text-foreground relative">
        <BinaryBackground />
        <MatrixTrail />
        <div className="relative z-20">
          <Header />
          <div className="container mx-auto px-6 py-24">
            <div className="text-center">
              <h1 className="text-4xl font-bold mb-6">Service Not Found</h1>
              <Link to="/#services">
                <Button className="bg-ai-gradient hover:opacity-90">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  {t('servicePages.backButton')}
                </Button>
              </Link>
            </div>
          </div>
          <Footer />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background text-foreground relative">
      <BinaryBackground />
      <MatrixTrail />
      <div className="relative z-20">
        <Header />

        <section className="py-24">
          <div className="container mx-auto px-6">
            <div
              ref={headerRef}
              className={`mb-12 animate-fade-in-up ${headerVisible ? 'visible' : ''}`}
            >
              <Link to="/#services" className="inline-flex items-center text-ai-cyan hover:text-ai-violet transition-colors mb-6">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t('servicePages.backButton')}
              </Link>

              {/* --- DIV REPLACED WITH IMG TAG HERE --- */}
              <img src={service.image} alt={service.title} className="w-16 h-16 rounded-lg object-cover mb-6 animate-glow-pulse" />

              <h1 className="text-5xl font-bold mb-6 text-gradient">{service.title}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl">{service.description}</p>
            </div>

            <div
              ref={contentRef}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16 animate-fade-in-up ${contentVisible ? 'visible' : ''}`}
            >
              <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30">
                <CardHeader>
                  <CardTitle className="text-2xl">{t('servicePages.overview')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-lg leading-relaxed">
                    {service.fullDescription}
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-ai-blue/10 to-ai-cyan/10 border-ai-blue/30">
                <CardHeader>
                  <CardTitle className="text-2xl">{t('servicePages.technologies')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    {service.technologies.map((tech, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 rounded-lg bg-card/50 backdrop-blur-sm">
                        <Check className="h-4 w-4 text-ai-cyan" />
                        <span className="text-sm">{tech}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div
              ref={featuresRef}
              className={`animate-scale-in ${featuresVisible ? 'visible' : ''}`}
            >
              <h2 className="text-3xl font-bold mb-8 text-center text-gradient">{t('servicePages.keyfeaures')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                {service.features.map((feature, index) => (
                  <Card key={index} className="bg-card/50 backdrop-blur-sm border-border hover:border-ai-purple/50 hover:glow-box transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-3">
                        <Check className="h-5 w-5 text-ai-cyan" />
                        <span className="font-medium">{feature}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div className="text-center">
              <Card className="bg-gradient-to-br from-ai-purple/10 to-ai-violet/10 border-ai-purple/30 max-w-2xl mx-auto">
                <CardHeader>
                  <CardTitle className="text-2xl">{t('servicePages.readyToGetStarted')}</CardTitle>
                  <CardDescription className="text-lg">
                    {t('servicePages.readyToGetStartedDescription')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Link to="/contact">
                    <Button size="lg" className="bg-ai-glow hover:scale-105 transition-all duration-300 px-8 py-4 text-lg animate-glow-pulse">
                      {t('servicePages.contactUsToday')}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default Services;