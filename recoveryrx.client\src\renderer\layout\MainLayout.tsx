import React from 'react';
import { SidebarProvider, SidebarTrigger, useSidebar } from '../components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { WindowHeader } from '../components/WindowHeader';
import { useLanguage } from '../i18n/LanguageContext';
import { LanguageSwitcher } from '../components/LanguageSwitcher';

// Import the fonts CSS
// import '../../assets/fonts/fonts.css';

interface MainLayoutProps {
  children: React.ReactNode;
}

// Create wrapper components to access sidebar context
function SidebarContainer({ children }: { children: React.ReactNode }) {
  const { state } = useSidebar();
  const isSidebarCollapsed = state === 'collapsed';
  
  return (
    <div className={`sidebar-container ${isSidebarCollapsed ? 'sidebar-hidden' : ''}`}>
      {children}
    </div>
  );
}

function MainLayoutContent({ children }: { children: React.ReactNode }) {
  const { isRTL } = useLanguage();
  const { state } = useSidebar();
  const isSidebarCollapsed = state === 'collapsed';

  return (
    <main className={`main-container ${isSidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
      <SidebarTrigger className={isRTL ? 'icon-flip-rtl' : ''} />
      {children}
    </main>
  );
}

export function MainLayout({ children }: MainLayoutProps) {
  const { isRTL } = useLanguage();

  // Apply RTL to document body when language changes
  React.useEffect(() => {
    if (isRTL) {
      document.documentElement.dir = 'rtl';
      document.body.classList.add('rtl', 'lang-fa');
    } else {
      document.documentElement.dir = 'ltr';
      document.body.classList.remove('rtl', 'lang-fa');
    }
  }, [isRTL]);

  return (
    <div className={isRTL ? 'rtl' : ''}>
      <WindowHeader>
        <div className="flex items-center">
          <LanguageSwitcher />
        </div>
      </WindowHeader>
      <div className="app-container">
        <SidebarProvider defaultOpen={true}>
          <SidebarContainer>
            <AppSidebar />
          </SidebarContainer>
          <MainLayoutContent>
            {children}
          </MainLayoutContent>
        </SidebarProvider>
      </div>
    </div>
  );
}
