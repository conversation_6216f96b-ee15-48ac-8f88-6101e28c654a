import path from 'path';
import { app } from 'electron';
import { isDev } from './environment.js';

export function getPreloadPath() {
  return path.join(
    app.getAppPath(),
    isDev() ? '.' : '..',
    '/dist-electron/preload.cjs'
  );
}

export function getUIPath() {
  return path.join(app.getAppPath(), '/dist-react/index.html');
}

export function getAssetPath() {
  //in dev mode assets are in src/assets
  //in production assets are in assets
  // return path.join(app.getAppPath(), isDev() ? '.' : '..', '/src/assets');
   if (isDev()) {
    return path.join(app.getAppPath(), './src/assets');
   } else {
    return path.join(app.getAppPath(), '../assets');
   }
}
