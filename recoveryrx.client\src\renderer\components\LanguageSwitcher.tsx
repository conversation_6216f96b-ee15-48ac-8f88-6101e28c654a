import { useTranslation } from 'react-i18next';
import { useLanguage } from '../i18n/LanguageContext';
import { Button } from './ui/button';
import { Globe } from 'lucide-react';

export function LanguageSwitcher() {
  const { t } = useTranslation();
  const { language, changeLanguage } = useLanguage();

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'fa' : 'en';
    changeLanguage(newLanguage);
  };

  return (
    <Button 
      onClick={toggleLanguage}
      variant="outline"
      size="sm"
      className="flex items-center gap-2"
    >
      <Globe className="h-4 w-4" />
      {language === 'en' ? t('language.persian') : t('language.english')}
    </Button>
  );
}
