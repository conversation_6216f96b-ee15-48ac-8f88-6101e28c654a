---
trigger: model_decision
description: "Rules for using shadcn/ui components in the RecoveryRx project"
---

# shadcn/ui Implementation Rules

You are an expert full-stack developer proficient in TypeScript, React, and modern UI/UX frameworks (e.g., Tailwind CSS, Shadcn UI, Radix UI). Your task is to produce the most optimized and maintainable React code, following best practices and adhering to the principles of clean code and robust architecture.

### Objective
- Create a React solution that is not only functional but also adheres to the best practices in performance, security, and maintainability.

### Code Style and Structure
- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Favor iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`).
- Structure files with exported components, subcomponents, helpers, static content, and types.
- Use lowercase with dashes for directory names (e.g., `components/auth-wizard`).

### UI and Styling
- Use modern UI frameworks (e.g., Tailwind CSS, Shadcn UI, Radix UI) for styling.
- Implement consistent design and responsive patterns across platforms.

## Core Principles

1. **Component Consistency**: Always use shadcn/ui components for UI elements when available instead of creating custom implementations.

2. **Design System Adherence**: Follow the established design tokens and variables defined in the project's shadcn/ui configuration.

3. **Accessibility First**: Leverage shadcn/ui's built-in accessibility features and never remove accessibility attributes.

4. **Composition Over Modification**: Compose components together rather than heavily modifying existing ones.

## Implementation Guidelines

### Component Imports

- Always use the established path aliases for importing components:
  ```tsx
  import { Button } from "@/components/ui/button";
  import { Input } from "@/components/ui/input";
  ```

- Never import shadcn/ui components from node_modules directly.

### Styling

- Use the `cn()` utility for combining class names:
  ```tsx
  import { cn } from "@/lib/utils";
  
  <Button className={cn("custom-class", isActive && "active")}>
  ```

- Maintain the design system by using Tailwind classes that align with the shadcn/ui theme.

- Use CSS variables defined in the theme for custom styling rather than hardcoded values.

### Component Variants

- Use the built-in variants provided by shadcn/ui components:
  ```tsx
  <Button variant="outline" size="sm">Small Outline Button</Button>
  ```

- When extending components with new variants, follow the same pattern as existing variants.

### Forms and Inputs

- Always use shadcn/ui form components with proper validation:
  ```tsx
  <Form {...form}>
    <FormField
      control={form.control}
      name="email"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input placeholder="<EMAIL>" {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  </Form>
  ```

- Maintain consistent form layouts using FormItem, FormLabel, FormControl, and FormMessage.

### Dialogs and Modals

- Use shadcn/ui Dialog components for all modal interfaces:
  ```tsx
  <Dialog>
    <DialogTrigger>Open Dialog</DialogTrigger>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Dialog Title</DialogTitle>
        <DialogDescription>Dialog description goes here.</DialogDescription>
      </DialogHeader>
      {/* Dialog content */}
    </DialogContent>
  </Dialog>
  ```

### Layout Components

- Use shadcn/ui layout components like Card, Sheet, and Tabs for consistent UI structure.

- Maintain proper component hierarchy as defined in the shadcn/ui documentation.

### Icons

- Use Lucide icons (the project's configured icon library) consistently:
  ```tsx
  import { Settings } from "lucide-react";
  
  <Button>
    <Settings className="mr-2 h-4 w-4" />
    Settings
  </Button>
  ```

### Dark Mode

- Ensure all components work correctly in both light and dark modes.

- Use the ThemeProvider and proper CSS variables for theme switching.

## Component Extensions

- When extending shadcn/ui components, create wrapper components in the appropriate directory structure.

- Document any extensions or modifications to standard components.

## Project-Specific Rules

- For the RecoveryRx Electron application, ensure all UI components are properly integrated with the IPC communication pattern.

- Maintain consistent styling between the application sidebar and main content areas.

- Use shadcn/ui's Toast component for all notifications and alerts in the application.

## Performance Considerations

- Import only the specific components needed rather than importing entire component libraries.

- Use React.memo or useMemo when appropriate for complex component renders.

## Documentation

- Document any custom variants or extensions to shadcn/ui components.

- Include examples of component usage in comments for complex implementations.