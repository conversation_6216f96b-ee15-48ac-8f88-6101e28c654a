﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36105.17
MinimumVisualStudioVersion = 10.0.40219.1
Project("{54A90642-561A-4BB1-A94E-469ADEE60C69}") = "recoveryrx.client", "recoveryrx.client\recoveryrx.client.esproj", "{309D360F-B12F-0228-13BB-6527BBD51923}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecoveryRx.Server", "RecoveryRx.Server\RecoveryRx.Server.csproj", "{D927E781-FFB0-47CD-92AA-8437EBD082E5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Migrations", "Migrations", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecoveryRx.Sqlite", "Migrations\RecoveryRx.Sqlite\RecoveryRx.Sqlite.csproj", "{059E2D7C-3FAC-0462-B324-B12C2ED7964B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecoveryRx.MySql", "Migrations\RecoveryRx.MySql\RecoveryRx.MySql.csproj", "{9CB90069-ABA3-9DAD-8BCB-A393005B2233}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecoveryRx.Models", "RecoveryRx.Models\RecoveryRx.Models.csproj", "{8885408B-9B04-6FF9-B7F5-3450D1C5A593}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{309D360F-B12F-0228-13BB-6527BBD51923}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{309D360F-B12F-0228-13BB-6527BBD51923}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{309D360F-B12F-0228-13BB-6527BBD51923}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{309D360F-B12F-0228-13BB-6527BBD51923}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{309D360F-B12F-0228-13BB-6527BBD51923}.Release|Any CPU.Build.0 = Release|Any CPU
		{309D360F-B12F-0228-13BB-6527BBD51923}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{D927E781-FFB0-47CD-92AA-8437EBD082E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D927E781-FFB0-47CD-92AA-8437EBD082E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D927E781-FFB0-47CD-92AA-8437EBD082E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D927E781-FFB0-47CD-92AA-8437EBD082E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{059E2D7C-3FAC-0462-B324-B12C2ED7964B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{059E2D7C-3FAC-0462-B324-B12C2ED7964B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{059E2D7C-3FAC-0462-B324-B12C2ED7964B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{059E2D7C-3FAC-0462-B324-B12C2ED7964B}.Release|Any CPU.Build.0 = Release|Any CPU
		{9CB90069-ABA3-9DAD-8BCB-A393005B2233}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9CB90069-ABA3-9DAD-8BCB-A393005B2233}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9CB90069-ABA3-9DAD-8BCB-A393005B2233}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9CB90069-ABA3-9DAD-8BCB-A393005B2233}.Release|Any CPU.Build.0 = Release|Any CPU
		{8885408B-9B04-6FF9-B7F5-3450D1C5A593}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8885408B-9B04-6FF9-B7F5-3450D1C5A593}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8885408B-9B04-6FF9-B7F5-3450D1C5A593}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8885408B-9B04-6FF9-B7F5-3450D1C5A593}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{059E2D7C-3FAC-0462-B324-B12C2ED7964B} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{9CB90069-ABA3-9DAD-8BCB-A393005B2233} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {78A2EFAB-B7EA-4289-8D7B-8EDF567BC7EA}
	EndGlobalSection
EndGlobal
