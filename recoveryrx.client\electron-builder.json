{"appId": "com.recoveryrx.app", "productName": "RecoveryRx", "directories": {"buildResources": "build"}, "files": ["dist-electron/**/*", "dist-react/**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "asarUnpack": ["resources/**"], "extraResources": ["dist-electron/preload.cjs", {"from": "src/assets", "to": "assets"}, {"from": "src/electron/ui/splash.html", "to": "splash.html"}, {"from": "resources/server/publish", "to": "server", "filter": ["**/*"]}], "mac": {"target": "dmg"}, "linux": {"target": "AppImage", "category": "Utility"}, "win": {"executableName": "RecoveryRx", "target": ["msi", "nsis"], "icon": "build/icons/icon.ico", "artifactName": "${productName}-${version}-setup.${ext}"}, "nsis": {"artifactName": "${productName}-${version}-setup.${ext}", "shortcutName": "${productName}", "uninstallDisplayName": "${productName}", "createDesktopShortcut": "always", "oneClick": false, "allowToChangeInstallationDirectory": true, "runAfterFinish": true, "license": "build/license.txt", "include": "build/installer.nsh"}, "publish": [{"provider": "github", "owner": "k<PERSON><PERSON><PERSON>i", "repo": "RecoveryRx-releases", "releaseType": "release", "private": true, "token": "*********************************************************************************************"}], "electronDownload": {"mirror": "https://npmmirror.com/mirrors/electron/"}}