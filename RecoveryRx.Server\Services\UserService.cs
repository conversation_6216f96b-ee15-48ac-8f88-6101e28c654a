using RecoveryRx.Server.Data.Repositories;
using RecoveryRx.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography; // For password hashing
using System.Text; // For password hashing
using System.Threading.Tasks;
using RecoveryRx.Server.DTOs.User;
using RecoveryRx.Server.DTOs.UserRole;

namespace RecoveryRx.Server.Services
{
    public class UserService : IUserService
    {
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository; // Needed for role validation

        public UserService(IUserRepository userRepository, IRoleRepository roleRepository)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
        }

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLowerInvariant();
            }
        }

        private UserDto MapUserToDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt,
                Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList()
            };
        }

        public async Task<UserDto?> GetUserByIdAsync(Guid id)
        {
            var user = await _userRepository.GetUserByIdAsync(id);
            return user == null ? null : MapUserToDto(user);
        }

        public async Task<UserDto?> GetUserByUsernameAsync(string username)
        {
            var user = await _userRepository.GetUserByUsernameAsync(username);
            return user == null ? null : MapUserToDto(user);
        }

        public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
        {
            var users = await _userRepository.GetAllUsersAsync();
            return users.Select(MapUserToDto);
        }

        public async Task<(UserDto? User, string? ErrorMessage)> CreateUserAsync(CreateUserDto createUserDto)
        {
            if (await _userRepository.UserExistsAsync(createUserDto.Username, createUserDto.Email))
            {
                return (null, "User with the same username or email already exists.");
            }

            var user = new User
            {
                Username = createUserDto.Username,
                Email = createUserDto.Email,
                PasswordHash = HashPassword(createUserDto.Password),
                FirstName = createUserDto.FirstName,
                LastName = createUserDto.LastName,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            var createdUser = await _userRepository.CreateUserAsync(user, createUserDto.Password); // Password here is for legacy, hash is used
            return (MapUserToDto(createdUser), null);
        }

        public async Task<(bool Success, string? ErrorMessage)> UpdateUserAsync(Guid id, UpdateUserDto updateUserDto)
        {
            var existingUser = await _userRepository.GetUserByIdAsync(id);
            if (existingUser == null)
            {
                return (false, "User not found.");
            }

            // Check for username/email conflicts if they are being changed
            if (updateUserDto.Username != null && updateUserDto.Username != existingUser.Username && await _userRepository.GetUserByUsernameAsync(updateUserDto.Username) != null)
            {
                return (false, "Username already taken.");
            }
            if (updateUserDto.Email != null && updateUserDto.Email != existingUser.Email && await _userRepository.GetUserByEmailAsync(updateUserDto.Email) != null)
            {
                return (false, "Email already taken.");
            }

            existingUser.Username = updateUserDto.Username ?? existingUser.Username;
            existingUser.Email = updateUserDto.Email ?? existingUser.Email;
            existingUser.FirstName = updateUserDto.FirstName ?? existingUser.FirstName;
            existingUser.LastName = updateUserDto.LastName ?? existingUser.LastName;
            existingUser.IsActive = updateUserDto.IsActive ?? existingUser.IsActive;
            existingUser.UpdatedAt = DateTime.UtcNow;

            var success = await _userRepository.UpdateUserAsync(existingUser);
            return (success, success ? null : "Failed to update user.");
        }

        public async Task<bool> DeleteUserAsync(Guid id)
        {
            return await _userRepository.DeleteUserAsync(id);
        }

        public async Task<(bool Success, string? ErrorMessage)> AssignRoleToUserAsync(AssignRoleToUserDto assignRoleDto)
        {
            var user = await _userRepository.GetUserByIdAsync(assignRoleDto.UserId);
            if (user == null) return (false, "User not found.");

            var role = await _roleRepository.GetRoleByNameAsync(assignRoleDto.RoleName);
            if (role == null) return (false, "Role not found.");

            if (await _userRepository.AssignRoleToUserAsync(assignRoleDto.UserId, assignRoleDto.RoleName))
            {
                return (true, null);
            }
            return (false, "Failed to assign role or role already assigned.");
        }

        public async Task<(bool Success, string? ErrorMessage)> RemoveRoleFromUserAsync(AssignRoleToUserDto removeRoleDto)
        {
             var user = await _userRepository.GetUserByIdAsync(removeRoleDto.UserId);
            if (user == null) return (false, "User not found.");

            var role = await _roleRepository.GetRoleByNameAsync(removeRoleDto.RoleName);
            if (role == null) return (false, "Role not found.");

            if (await _userRepository.RemoveRoleFromUserAsync(removeRoleDto.UserId, removeRoleDto.RoleName))
            {
                return (true, null);
            }
            return (false, "Failed to remove role or role not assigned to user.");
        }
    }
}
