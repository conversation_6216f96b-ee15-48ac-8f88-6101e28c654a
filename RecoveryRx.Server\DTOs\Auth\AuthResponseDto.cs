using System;
using System.Collections.Generic;

namespace RecoveryRx.Server.DTOs.Auth
{
    public class AuthResponseDto
    {
        public Guid UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public DateTime Expiration { get; set; }
        public IEnumerable<string> Roles { get; set; } = new List<string>();
    }
}
