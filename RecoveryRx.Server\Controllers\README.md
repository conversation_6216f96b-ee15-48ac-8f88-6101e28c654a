# RecoveryRx API Controllers Documentation

This document provides an overview of all API controllers available in the RecoveryRx.Server project. Each controller is responsible for a specific domain of functionality and exposes various endpoints for client applications to interact with.

## Base URL

All API endpoints are relative to the base URL: `http://localhost:5000`

## Available Controllers

- [Database Controller](#database-controller)
- [Patient Controller](#patient-controller)
- [Users Controller](#users-controller)
- [Roles Controller](#roles-controller)
- [Localization Test Controller](#localization-test-controller)

---

## Database Controller

**Route Prefix**: `/api/database`

The Database Controller manages database operations such as testing connections, retrieving provider information, initializing the database, and applying migrations.

### Endpoints

#### GET `/api/database/test-connection`

Tests the current database connection and returns status, provider, and connection details.

**Response**:
- `200 OK`: Returns the database connection status, provider, and sanitized connection details.
- `500 Internal Server Error`: If an unexpected error occurs during the connection test.

#### GET `/api/database/provider`

Gets the currently configured database provider (e.g., SQLite, MySQL).

**Response**:
- `200 OK`: Returns the name of the current database provider.
- `500 Internal Server Error`: If an unexpected error occurs while retrieving the provider.

#### POST `/api/database/initialize`

Initializes the database. This typically involves ensuring the database schema is created.

**Response**:
- `200 OK`: Returns details of the database initialization process.
- `500 Internal Server Error`: If an unexpected error occurs during initialization.

#### POST `/api/database/apply-migrations`

Applies any pending Entity Framework Core migrations to the database for the current provider.

**Response**:
- `200 OK`: Returns details of the migration application process.
- `500 Internal Server Error`: If an unexpected error occurs while applying migrations.

#### POST `/api/database/configure-provider`

Configures the database provider and connection settings in `appsettings.json`.

**Request Body**:
```json
{
  "provider": "string", // "SQLite" or "MySQL"
  "sqliteSettings": {
    "databasePath": "string" // Path to SQLite database file
  },
  "mySqlSettings": {
    "server": "string",
    "port": "number",
    "database": "string",
    "userId": "string",
    "password": "string"
  }
}
```

**Response**:
- `200 OK`: Returns a success message if the settings are updated successfully.
- `400 Bad Request`: If the input settings are invalid or the update fails for a known reason.
- `500 Internal Server Error`: Returns an error message if an internal server error occurs during the update.

**Note**: An application restart is typically required for these changes to take full effect.

---

## Patient Controller

**Route Prefix**: `/api/patient`

The Patient Controller manages patient-related operations such as creating, retrieving, updating, and deleting patients.

### Endpoints

#### GET `/api/patient`

Gets all active patients.

**Response**:
- `200 OK`: Returns the list of patients.

#### GET `/api/patient/{id}`

Gets a specific patient by id.

**Parameters**:
- `id`: The patient id (long)

**Response**:
- `200 OK`: Returns the patient.
- `404 Not Found`: If the patient is not found.

#### POST `/api/patient`

Creates a new patient.

**Request Body**:
```json
{
  "firstName": "string",
  "lastName": "string",
  "dateOfBirth": "string (yyyy-MM-dd)",
  "gender": "number",
  "email": "string",
  "phoneNumber": "string",
  "address": "string"
}
```

**Response**:
- `201 Created`: Returns the newly created patient.
- `400 Bad Request`: If the patient data is invalid.

#### PUT `/api/patient/{id}`

Updates an existing patient.

**Parameters**:
- `id`: The patient id (long)

**Request Body**:
```json
{
  "firstName": "string",
  "lastName": "string",
  "dateOfBirth": "string (yyyy-MM-dd)",
  "gender": "number",
  "email": "string",
  "phoneNumber": "string",
  "address": "string"
}
```

**Response**:
- `204 No Content`: If the patient was successfully updated.
- `400 Bad Request`: If the patient data is invalid.
- `404 Not Found`: If the patient is not found.

#### DELETE `/api/patient/{id}`

Deletes a patient (soft delete).

**Parameters**:
- `id`: The patient id (long)

**Response**:
- `204 No Content`: If the patient was successfully deleted.
- `404 Not Found`: If the patient is not found.

---

## Users Controller

**Route Prefix**: `/api/users`

The Users Controller manages user-related operations such as creating, retrieving, updating, and deleting users, as well as assigning and removing roles.

### Endpoints

#### GET `/api/users`

Gets all users.

**Response**:
- `200 OK`: Returns the list of users.

#### GET `/api/users/{id}`

Gets a specific user by id.

**Parameters**:
- `id`: The user id (long)

**Response**:
- `200 OK`: Returns the user.
- `404 Not Found`: If the user is not found.

#### GET `/api/users/username/{username}`

Gets a specific user by username.

**Parameters**:
- `username`: The username (string)

**Response**:
- `200 OK`: Returns the user.
- `404 Not Found`: If the user is not found.

#### POST `/api/users`

Creates a new user.

**Request Body**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string"
}
```

**Response**:
- `201 Created`: Returns the newly created user.
- `400 Bad Request`: If the user data is invalid.

#### PUT `/api/users/{id}`

Updates an existing user.

**Parameters**:
- `id`: The user id (long)

**Request Body**:
```json
{
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string"
}
```

**Response**:
- `204 No Content`: If the user was successfully updated.
- `400 Bad Request`: If the user data is invalid.
- `404 Not Found`: If the user is not found.

#### DELETE `/api/users/{id}`

Deletes a user.

**Parameters**:
- `id`: The user id (long)

**Response**:
- `204 No Content`: If the user was successfully deleted.
- `404 Not Found`: If the user is not found or could not be deleted.

#### POST `/api/users/assign-role`

Assigns a role to a user.

**Request Body**:
```json
{
  "userId": "long",
  "roleId": "int"
}
```

**Response**:
- `200 OK`: If the role was successfully assigned.
- `400 Bad Request`: If the assignment data is invalid or the assignment failed.

#### POST `/api/users/remove-role`

Removes a role from a user.

**Request Body**:
```json
{
  "userId": "long",
  "roleId": "int"
}
```

**Response**:
- `200 OK`: If the role was successfully removed.
- `400 Bad Request`: If the removal data is invalid or the removal failed.

---

## Roles Controller

**Route Prefix**: `/api/roles`

The Roles Controller manages role-related operations such as creating, retrieving, updating, and deleting roles.

### Endpoints

#### GET `/api/roles`

Gets all roles.

**Response**:
- `200 OK`: Returns the list of roles.

#### GET `/api/roles/{id}`

Gets a specific role by id.

**Parameters**:
- `id`: The role id (int)

**Response**:
- `200 OK`: Returns the role.
- `404 Not Found`: If the role is not found.

#### GET `/api/roles/name/{name}`

Gets a specific role by name.

**Parameters**:
- `name`: The role name (string)

**Response**:
- `200 OK`: Returns the role.
- `404 Not Found`: If the role is not found.

#### POST `/api/roles`

Creates a new role.

**Request Body**:
```json
{
  "name": "string",
  "description": "string"
}
```

**Response**:
- `201 Created`: Returns the newly created role.
- `400 Bad Request`: If the role data is invalid.

#### PUT `/api/roles/{id}`

Updates an existing role.

**Parameters**:
- `id`: The role id (int)

**Request Body**:
```json
{
  "name": "string",
  "description": "string"
}
```

**Response**:
- `204 No Content`: If the role was successfully updated.
- `400 Bad Request`: If the role data is invalid.
- `404 Not Found`: If the role is not found.

#### DELETE `/api/roles/{id}`

Deletes a role.

**Parameters**:
- `id`: The role id (int)

**Response**:
- `204 No Content`: If the role was successfully deleted.
- `404 Not Found`: If the role is not found or could not be deleted.

---

## Localization Test Controller

**Route Prefix**: `/api/localizationtest`

The Localization Test Controller provides endpoints for testing localization functionality.

### Endpoints

#### GET `/api/localizationtest/gender-values`

Tests localization for gender values.

**Headers**:
- `Accept-Language`: The preferred language (e.g., "en-US", "es-ES")

**Response**:
- `200 OK`: Returns localized gender values based on the Accept-Language header.
