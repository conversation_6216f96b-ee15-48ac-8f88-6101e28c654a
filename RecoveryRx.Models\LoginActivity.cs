using System;
using System.ComponentModel.DataAnnotations;

namespace RecoveryRx.Models
{
    public class LoginActivity
    {
        [Key]
        public Guid Id { get; set; }
        
        [Required]
        public Guid UserId { get; set; }
        
        [Required]
        [MaxLength(256)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        public DateTime LoginTime { get; set; } = DateTime.UtcNow;
        
        [MaxLength(50)]
        public string IpAddress { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string UserAgent { get; set; } = string.Empty;
        
        public bool Success { get; set; }
        
        [MaxLength(500)]
        public string? FailureReason { get; set; }
        
        // Navigation property
        public User User { get; set; } = null!;
    }
}
