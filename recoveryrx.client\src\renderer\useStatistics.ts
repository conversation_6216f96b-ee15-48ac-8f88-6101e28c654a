import { useEffect, useState } from 'react';
import { electronBridge } from './utils/electronBridge';
import type { Statistics } from './utils/electronBridge';

export function useStatistics(dataPointCount: number): Statistics[] {
  const [value, setValue] = useState<Statistics[]>([]);

  useEffect(() => {
    const unsub = electronBridge.subscribeStatistics((stats) =>
      setValue((prev) => {
        const newData = [...prev, stats];

        if (newData.length > dataPointCount) {
          newData.shift();
        }

        return newData;
      })
    );
    return unsub;
  }, [dataPointCount]);

  return value;
}
