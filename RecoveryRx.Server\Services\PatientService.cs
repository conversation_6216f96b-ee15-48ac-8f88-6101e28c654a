using Microsoft.Extensions.Localization;
using RecoveryRx.Models;
using RecoveryRx.Server.Data.Repositories;
using RecoveryRx.Server.DTOs;
using RecoveryRx.Server.Helpers;
using RecoveryRx.Server.Resources;

namespace RecoveryRx.Server.Services;

public class PatientService : IPatientService
{
    private readonly IPatientRepository _patientRepository;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PatientService(IPatientRepository patientRepository, IStringLocalizer<SharedResource> localizer)
    {
        _patientRepository = patientRepository;
        _localizer = localizer;
    }

    public async Task<IEnumerable<PatientDto>> GetAllPatientsAsync()
    {
        var patients = await _patientRepository.GetAllAsync();
        
        return patients.Select(p => MapToPatientDto(p));
    }

    public async Task<PatientDto?> GetPatientByIdAsync(long id)
    {
        var patient = await _patientRepository.GetByIdAsync(id);
        
        return patient != null ? MapToPatientDto(patient) : null;
    }

    public async Task<PatientDto> CreatePatientAsync(CreateUpdatePatientDto createPatientDto)
    {
        var patient = new Patient
        {
            FirstName = createPatientDto.FirstName,
            LastName = createPatientDto.LastName,
            NationalIdNumber = createPatientDto.NationalIdNumber,
            Gender = createPatientDto.Gender,
            DateOfBirth = createPatientDto.DateOfBirth,
            MobileNumber = createPatientDto.MobileNumber,
            LandlineNumber = createPatientDto.LandlineNumber,
            Address = createPatientDto.Address,
            ImagePath = createPatientDto.ImagePath
        };
        
        var createdPatient = await _patientRepository.CreateAsync(patient);
        
        return MapToPatientDto(createdPatient);
    }

    public async Task<PatientDto?> UpdatePatientAsync(long id, CreateUpdatePatientDto updatePatientDto)
    {
        var existingPatient = await _patientRepository.GetByIdAsync(id);
        
        if (existingPatient == null)
        {
            return null;
        }
        
        existingPatient.FirstName = updatePatientDto.FirstName;
        existingPatient.LastName = updatePatientDto.LastName;
        existingPatient.NationalIdNumber = updatePatientDto.NationalIdNumber;
        existingPatient.Gender = updatePatientDto.Gender;
        existingPatient.DateOfBirth = updatePatientDto.DateOfBirth;
        existingPatient.MobileNumber = updatePatientDto.MobileNumber;
        existingPatient.LandlineNumber = updatePatientDto.LandlineNumber;
        existingPatient.Address = updatePatientDto.Address;
        existingPatient.ImagePath = updatePatientDto.ImagePath;
        
        var updatedPatient = await _patientRepository.UpdateAsync(existingPatient);
        
        return updatedPatient != null ? MapToPatientDto(updatedPatient) : null;
    }

    public async Task<bool> DeletePatientAsync(long id)
    {
        return await _patientRepository.DeleteAsync(id);
    }
    
    private PatientDto MapToPatientDto(Patient patient)
    {
        return new PatientDto
        {
            Id = patient.Id,
            FirstName = patient.FirstName,
            LastName = patient.LastName,
            NationalIdNumber = patient.NationalIdNumber,
            Gender = patient.Gender,
            GenderName = GenderHelper.GetLocalizedGenderName(patient.Gender),
            DateOfBirth = patient.DateOfBirth,
            MobileNumber = patient.MobileNumber,
            LandlineNumber = patient.LandlineNumber,
            Address = patient.Address,
            ImagePath = patient.ImagePath,
            CreatedAt = patient.CreatedAt,
            UpdatedAt = patient.UpdatedAt
        };
    }
}
