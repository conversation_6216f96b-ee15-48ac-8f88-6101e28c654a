# Load environment variables from .env file
$envFile = Join-Path $PSScriptRoot ".env"
if (Test-Path $envFile) {
    Write-Host "Loading environment variables from .env file..." -ForegroundColor Cyan
    Get-Content $envFile | ForEach-Object {
        if ($_ -match '^\s*([^#][^=]+)=(.*)$') {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            [Environment]::SetEnvironmentVariable($name, $value, "Process")
            Write-Host "Set environment variable: $name" -ForegroundColor Green
        }
    }
} else {
    Write-Host "No .env file found. Please create one with your GH_TOKEN." -ForegroundColor Yellow
    exit 1
}

# Verify GH_TOKEN is set
if (-not $env:GH_TOKEN) {
    Write-Host "GH_TOKEN is not set! Please check your .env file." -ForegroundColor Red
    exit 1
}

Write-Host "GH_TOKEN is set. Ready to build and publish." -ForegroundColor Green
