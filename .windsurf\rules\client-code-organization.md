---
trigger: model_decision
description: "Rules for organizing code in the RecoveryRx client application"
---

# RecoveryRx Client Code Organization Rules

## Core Structure

1. **Separation of Concerns**:
   - UI code MUST be placed in `recoveryrx.client/src/renderer`
   - Electron-specific code MUST be placed in `recoveryrx.client/src/electron`

2. **No Cross-Contamination**:
   - React components should NOT directly import from the `electron` directory
   - Use the established bridge pattern for communication between UI and Electron

## UI Directory Structure

1. **Component Organization**:
   - Place reusable UI components in `src/renderer/components`
   - Group related components in subdirectories
   - Use shadcn/ui components whenever possible (see shadcn-ui.md rules)

2. **Page Structure**:
   - Place page components in `src/renderer/pages`
   - Each page should be in its own directory with related components

3. **State Management**:
   - Use React Context for global state management
   - Place context providers in `src/renderer/contexts`
   - Keep state close to where it's used when possible

4. **Utilities**:
   - Place UI-specific utilities in `src/renderer/utils`
   - Include proper TypeScript typing for all utilities

5. **Hooks**:
   - Place custom hooks in `src/renderer/hooks`
   - Follow React hook naming convention (use`use` prefix)

## Electron Directory Structure

1. **Main Process**:
   - Place main process code in `src/electron/main`
   - Separate concerns into different modules

2. **IPC Communication**:
   - Define IPC channels in `src/electron/ipc`
   - Use typed messages for IPC communication
   - Follow the established pattern for API communication:
     - Renderer → Main (IPC) → API Server → Main → Renderer (IPC)

3. **API Integration**:
   - Place API client code in `src/electron/api`
   - Use typed requests and responses
   - Handle errors consistently

4. **Configuration**:
   - Place Electron configuration in `src/electron/config`
   - Use environment variables for different environments

## Coding Standards

1. **TypeScript**:
   - Use TypeScript for all new code
   - Define interfaces/types in separate files when shared
   - Use proper type annotations for all functions and variables

2. **Imports**:
   - Use absolute imports with path aliases
   - Group imports logically (React, components, utilities, etc.)
   - Avoid circular dependencies

3. **Component Structure**:
   - Use functional components with hooks
   - Extract complex logic into custom hooks
   - Keep components focused on a single responsibility

4. **Error Handling**:
   - Implement proper error boundaries
   - Use consistent error handling patterns
   - Log errors appropriately

5. **Testing**:
   - Write tests for critical functionality
   - Place tests alongside the code they test
   - Mock external dependencies appropriately

## Communication Between UI and Electron

1. **Electron Bridge**:
   - Use the established `electronBridge` utility for all communication
   - Never directly access Electron APIs from UI code
   - Follow the typed message pattern for all IPC calls

2. **API Requests**:
   - All API requests MUST go through the main process
   - Never make direct API calls from the renderer process
   - Handle API errors consistently

## Performance Considerations

1. **Rendering Optimization**:
   - Use React.memo for expensive components
   - Implement virtualization for long lists
   - Avoid unnecessary re-renders

2. **Resource Management**:
   - Clean up resources in useEffect cleanup functions
   - Be mindful of memory usage in Electron processes
   - Implement proper garbage collection practices