'use client';
import { Box, Container, Button } from '@mui/material';
import TelegramIcon from '@mui/icons-material/Telegram';
import { Slide } from 'react-awesome-reveal';
import Image from 'next/image'; // Import the Image component

const HeroSection: React.FC = () => {
  return (
    <Box
      sx={{
        position: 'relative',
        height: { xs: '80vh', md: '90vh' },
        overflow: 'hidden',
        backgroundColor: 'primary.main',
      }}
    >
      <Box
        component="iframe"
        src="https://www.theasys.io/viewer/b5svGCLf1X1NZi0RnMcyHyFqzFRPXA/"
        allowFullScreen
        allow="vr;gyroscope;accelerometer"
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          width: '100%',
          height: '100%',
          transform: 'translate(-50%, -50%)',
          zIndex: 0,
          border: 'none',
        }}
      />
      <Container
        maxWidth="lg"
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between', // Distribute space
          alignItems: 'flex-start', // Align items to the start (left)
          padding: '20px',
          pointerEvents: 'none',
        }}
      >
        {/* Logo at the top-left */}
        <Box sx={{ mt: 2, ml: 2, pointerEvents: 'auto' }}>
          <Image
            src="/logo_black.png" // Replace with your actual placeholder image path
            alt="Your Company Logo"
            width={150} // Adjust the width for a smaller size
            height={75} // Adjust the height to maintain aspect ratio
            style={{
              maxWidth: '100%',
              height: 'auto',
            }}
          />
        </Box>

        {/* Bottom Buttons aligned to the left */}
        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-start', mb: 4, ml: 2, pointerEvents: 'auto' }}>
          <Slide direction="up" triggerOnce delay={400}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, pointerEvents: 'none' }}>
              <Button
                href="#contact-us"
                variant="contained"
                color="secondary"
                size="large"
                startIcon={<TelegramIcon />}
                sx={{ pointerEvents: 'auto', color: '#000000', backgroundColor: '#FFFFFF' }}
              >
                Get a Free Consultation
              </Button>
            </Box>
          </Slide>
        </Box>
      </Container>
    </Box>
  );
};

export default HeroSection;