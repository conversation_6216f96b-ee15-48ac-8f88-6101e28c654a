
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowDown } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { useLanguage } from "@/contexts/LanguageContext";
import CostCalculatorModal from "./CostCalculatorModal";

const HeroSection = () => {
  const [heroRef, heroVisible] = useScrollAnimation(0.1);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.2);
  const { t, isRTL } = useLanguage();
  const [isCalculatorOpen, setIsCalculatorOpen] = useState(false);

  return (
    <>
      <section className="min-h-screen flex items-center justify-center relative overflow-hidden cyber-grid pt-5">
        <div className="absolute inset-0 bg-gradient-to-br from-ai-purple/20 via-transparent to-ai-blue/20"></div>
        
        <div className="container mx-auto px-6 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <div 
              ref={heroRef}
              className={`animate-fade-in-up ${heroVisible ? 'visible' : ''} animate-float`}
            >
              <h1 className="text-6xl md:text-8xl font-black mb-6 leading-tight text-center">
                {t('hero.title')}
                <span className="text-gradient animate-gradient-shift bg-size-200">
                  <br/>
                  {t('hero.titleColor')}
                </span>
              </h1>
            </div>
            
            <p className={`text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed animate-fade-in-up text-center ${heroVisible ? 'visible' : ''}`} 
               style={{"--delay": "0.2s"} as React.CSSProperties}>
              {t('hero.subtitle')}
            </p>
            
            <div 
              ref={ctaRef}
              className={`flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-scale-in ${ctaVisible ? 'visible' : ''}`}
              style={{"--delay": "0.4s"} as React.CSSProperties}
            >
              <Button 
                size="lg" 
                className="bg-ai-glow hover:scale-105 transition-all duration-300 px-8 py-4 text-lg animate-glow-pulse"
                onClick={() => setIsCalculatorOpen(true)}
              >
                {t('hero.cta1')}
              </Button>
            </div>
            
            <div className={`animate-bounce animate-fade-in-up flex justify-center ${ctaVisible ? 'visible' : ''}`}
                 style={{"--delay": "0.6s"} as React.CSSProperties}>
              <ArrowDown className="h-8 w-8 text-ai-purple" />
            </div>
          </div>
        </div>
        
        <div className={`absolute top-20 w-72 h-72 bg-ai-purple/10 rounded-full blur-3xl animate-float ${isRTL ? 'right-10' : 'left-10'}`}></div>
        <div className={`absolute bottom-20 w-96 h-96 bg-ai-blue/10 rounded-full blur-3xl animate-float ${isRTL ? 'left-10' : 'right-10'}`} style={{animationDelay: '1s'}}></div>
      </section>
      
      <CostCalculatorModal 
        isOpen={isCalculatorOpen} 
        onClose={() => setIsCalculatorOpen(false)} 
      />
    </>
  );
};

export default HeroSection;
