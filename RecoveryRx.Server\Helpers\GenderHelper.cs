using Microsoft.Extensions.Localization;
using RecoveryRx.Models;
using RecoveryRx.Server.Resources;
using System.Globalization;
using System.Threading;

namespace RecoveryRx.Server.Helpers;

public static class GenderHelper
{
    public static string GetLocalizedGenderName(Gender gender, IStringLocalizer<SharedResource> localizer)
    {
        return gender switch
        {
            Gender.Male => localizer["Gender_Male"].Value,
            Gender.Female => localizer["Gender_Female"].Value,
            Gender.Other => localizer["Gender_Other"].Value,
            _ => string.Empty
        };
    }
    
    // Alternative method using strongly-typed resources
    public static string GetLocalizedGenderName(Gender gender)
    {
        return gender switch
        {
            Gender.Male => SharedResource.Gender_Male,
            Gender.Female => SharedResource.Gender_Female,
            Gender.Other => SharedResource.Gender_Other,
            _ => string.Empty
        };
    }
}
