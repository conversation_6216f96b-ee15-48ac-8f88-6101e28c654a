// Script to copy splash.html to dist-electron
import fs from 'fs';
import path from 'path';

const sourceFile = path.join(process.cwd(), 'src', 'electron', 'ui', 'splash.html');
const targetDir = path.join(process.cwd(), 'dist-electron', 'ui');
const targetFile = path.join(targetDir, 'splash.html');

// Create dist-electron directory if it doesn't exist
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Copy splash.html to dist-electron
fs.copyFileSync(sourceFile, targetFile);

console.log(`Successfully copied splash.html to ${targetFile}`);
