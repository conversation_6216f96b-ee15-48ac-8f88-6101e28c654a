# RecoveryRx Authentication System

This document describes the authentication system implemented for the RecoveryRx application.

## Overview

The authentication system provides:

- User registration and login
- JWT-based authentication
- Role-based authorization
- Password hashing using BCrypt
- Login activity tracking
- Security tools for JWT key and admin password setup

## Components

### DTOs

- `RegisterRequestDto`: User registration data
- `LoginRequestDto`: User login credentials
- `AuthResponseDto`: Authentication response with JWT token
- `LoginActivityDto`: Login activity data

### Models

- `User`: User entity with username, email, and password hash
- `Role`: Role entity for authorization
- `UserRole`: Many-to-many relationship between users and roles
- `LoginActivity`: Tracks login attempts and activity

### Services

- `AuthService`: Main authentication service for registration and login
- `PasswordService`: Handles password hashing and verification using BCrypt
- `TokenService`: Generates and validates JWT tokens
- `LoginActivityService`: Tracks and manages login activity

### Controllers

- `AuthController`: API endpoints for authentication operations

## Setup Instructions

### 1. Generate a Secure JWT Key
 

### 2. Set Up Admin Password
 

### 3. Apply Database Migrations

After setting up the authentication components, apply the database migrations:

```bash
dotnet ef database update -- --provider Sqlite
# or for MySQL
dotnet ef database update -- --provider MySql
```

## API Endpoints

### Registration

```
POST /api/auth/register
```

Request body:
```json
{
  "username": "user1",
  "email": "<EMAIL>",
  "password": "SecurePassword123",
  "confirmPassword": "SecurePassword123",
  "firstName": "John",
  "lastName": "Doe"
}
```

### Login

```
POST /api/auth/login
```

Request body:
```json
{
  "username": "user1",
  "password": "SecurePassword123"
}
```

### Get Current User

```
GET /api/auth/me
```

Authorization: Bearer Token

### View Login Activity (Admin only)

```
GET /api/auth/login-activity
```

Authorization: Bearer Token (Admin role required)

### View Personal Login History

```
GET /api/auth/my-login-history
```

Authorization: Bearer Token

### Clean Up Old Login Records (Admin only)

```
POST /api/auth/cleanup-login-records?daysToKeep=90
```

Authorization: Bearer Token (Admin role required)

## Security Considerations

1. **JWT Key**: Keep your JWT key secure and don't commit it to source control.
2. **Password Storage**: Passwords are hashed using BCrypt before storage.
3. **Login Activity**: Login attempts are logged for security monitoring.
4. **Database Size**: Old login records can be cleaned up to manage database size.
5. **Default Admin**: Change the default admin password immediately in production.

## Integration with Electron Client

The authentication system is designed to work with the Electron client application using the Electron Bridge pattern for secure communication between the renderer and main processes.

For more information on the Electron Bridge pattern, see the [electron-bridge-pattern.md](../.windsurf/rules/electron-bridge-pattern.md) documentation.
