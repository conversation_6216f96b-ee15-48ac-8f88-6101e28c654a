using Microsoft.AspNetCore.Mvc;
using RecoveryRx.Server.DTOs;
using RecoveryRx.Server.Services;

namespace RecoveryRx.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class PatientController : ControllerBase
{
    private readonly IPatientService _patientService;

    public PatientController(IPatientService patientService)
    {
        _patientService = patientService;
    }

    /// <summary>
    /// Gets all active patients
    /// </summary>
    /// <returns>A list of patients</returns>
    /// <response code="200">Returns the list of patients</response>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<PatientDto>>> GetAllPatients()
    {
        var patients = await _patientService.GetAllPatientsAsync();
        return Ok(patients);
    }

    /// <summary>
    /// Gets a specific patient by id
    /// </summary>
    /// <param name="id">The patient id</param>
    /// <returns>The patient details</returns>
    /// <response code="200">Returns the patient</response>
    /// <response code="404">If the patient is not found</response>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<PatientDto>> GetPatientById(long id)
    {
        var patient = await _patientService.GetPatientByIdAsync(id);
        
        if (patient == null)
        {
            return NotFound();
        }
        
        return Ok(patient);
    }

    /// <summary>
    /// Creates a new patient
    /// </summary>
    /// <param name="createPatientDto">The patient data</param>
    /// <returns>The created patient</returns>
    /// <response code="201">Returns the newly created patient</response>
    /// <response code="400">If the patient data is invalid</response>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PatientDto>> CreatePatient(CreateUpdatePatientDto createPatientDto)
    {
        var patient = await _patientService.CreatePatientAsync(createPatientDto);
        
        return CreatedAtAction(nameof(GetPatientById), new { id = patient.Id }, patient);
    }

    /// <summary>
    /// Updates an existing patient
    /// </summary>
    /// <param name="id">The patient id</param>
    /// <param name="updatePatientDto">The updated patient data</param>
    /// <returns>No content</returns>
    /// <response code="204">If the patient was successfully updated</response>
    /// <response code="400">If the patient data is invalid</response>
    /// <response code="404">If the patient is not found</response>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdatePatient(long id, CreateUpdatePatientDto updatePatientDto)
    {
        var patient = await _patientService.UpdatePatientAsync(id, updatePatientDto);
        
        if (patient == null)
        {
            return NotFound();
        }
        
        return NoContent();
    }

    /// <summary>
    /// Deletes a patient (soft delete)
    /// </summary>
    /// <param name="id">The patient id</param>
    /// <returns>No content</returns>
    /// <response code="204">If the patient was successfully deleted</response>
    /// <response code="404">If the patient is not found</response>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeletePatient(long id)
    {
        var result = await _patientService.DeletePatientAsync(id);
        
        if (!result)
        {
            return NotFound();
        }
        
        return NoContent();
    }
}
