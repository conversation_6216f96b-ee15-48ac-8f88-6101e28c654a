using System.ComponentModel.DataAnnotations;

namespace RecoveryRx.Server.DTOs.User
{
    public class UpdateUserDto
    {
        [MaxLength(256)]
        public string? Username { get; set; }

        [EmailAddress]
        [MaxLength(256)]
        public string? Email { get; set; }

        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool? IsActive { get; set; }
    }
}
