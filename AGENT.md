# RecoveryRx Development Guidelines

## Build Commands
- **Run Client + Server**: `cd recoveryrx.client && npm run dev`
- **Client Only**: `cd recoveryrx.client && npm run dev:react`
- **Server Only**: `cd RecoveryRx.Server && dotnet run`
- **Build Client**: `cd recoveryrx.client && npm run build`
- **Unit Tests**: `cd recoveryrx.client && npm run test:unit`
- **E2E Tests**: `cd recoveryrx.client && npm run test:e2e`
- **Run Single Test**: `cd recoveryrx.client && npm run test:unit -- -t "test name"`

## Code Style Guidelines
- **Client Structure**: UI code in `src/renderer`, Electron code in `src/electron`
- **Imports**: Use absolute imports with path aliases, group logically
- **Components**: Use functional components with hooks, follow shadcn/ui patterns
- **TypeScript**: Use proper types for all functions and variables
- **Naming**: PascalCase for components, camelCase for variables/functions
- **Error Handling**: Implement error boundaries, use consistent patterns
- **API Communication**: All API requests through main process using IPC bridge
- **Server**: Follow Clean Architecture, keep controllers thin, use proper HTTP status codes
- **Testing**: Write tests for critical functionality, place tests alongside code