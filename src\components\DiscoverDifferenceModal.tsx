import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Check, Zap, Brain, Rocket } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface DiscoverDifferenceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DiscoverDifferenceModal = ({ isOpen, onClose }: DiscoverDifferenceModalProps) => {
  const { t } = useLanguage();

  const differences = [
    {
      icon: <Brain className="h-8 w-8 text-ai-purple" />,
      title: t('cost.discover.first'),
      description: t('cost.discover.firstDescription'),
      gradient: "from-ai-purple/20 to-ai-violet/20"
    },
    {
      icon: <Zap className="h-8 w-8 text-ai-cyan" />,
      title: t('cost.discover.second'),
      description: t('cost.discover.secondDescription'),
      gradient: "from-ai-cyan/20 to-ai-blue/20"
    },
    {
      icon: <Rocket className="h-8 w-8 text-ai-pink" />,
      title: t('cost.discover.third'),
      description: t('cost.discover.thirdDescription'),
      gradient: "from-ai-pink/20 to-ai-violet/20"
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl mx-auto bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30">
        <DialogHeader>
          <DialogTitle className="text-3xl text-gradient text-center mb-6">
            {t('cost.discover.title')}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {differences.map((diff, index) => (
            <Card key={index} className={`bg-gradient-to-br ${diff.gradient} border-ai-purple/30 hover:glow-box transition-all duration-300`}>
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  {diff.icon}
                </div>
                <CardTitle className="text-xl">{diff.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center text-base">
                  {diff.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="bg-gradient-to-r from-ai-purple/10 to-ai-blue/10 rounded-lg p-6 mb-6">
          <h3 className="text-2xl font-bold text-center mb-4 text-gradient">
            {t('cost.discover.traditional')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-lg font-semibold mb-3 text-muted-foreground">{t('cost.discover.traditionalDevelopment')}</h4>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  {t('cost.discover.traditionalFirst')}
                </li>
                <li className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  {t('cost.discover.traditionalSecond')}
                </li>
                <li className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  {t('cost.discover.traditionalThird')}
                </li>
                <li className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  {t('cost.discover.traditionalFourth')}
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-3 text-ai-cyan">{t('cost.discover.maxqitApproach')}</h4>
              <ul className="space-y-2">
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-ai-cyan" />
                  {t('cost.discover.maxqitFirst')}
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-ai-cyan" />
                  {t('cost.discover.maxqitSecond')}
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-ai-cyan" />
                  {t('cost.discover.maxqitThird')}
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-ai-cyan" />
                  {t('cost.discover.maxqitFourth')}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-center">
          <Link to="/contact">
            <Button
              size="lg"
              className="bg-ai-glow hover:scale-105 transition-all duration-300 px-8 py-4 text-lg animate-glow-pulse"
            >
              {t('cost.discover.cta')}
            </Button>
          </Link>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DiscoverDifferenceModal;
