
@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=Space+Grotesk:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    --background: 5 10% 8%;
    --foreground: 210 40% 98%;

    --card: 5 10% 10%;
    --card-foreground: 210 40% 98%;

    --popover: 5 10% 10%;
    --popover-foreground: 210 40% 98%;

    --primary: 263 70% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 215 25% 16%;
    --secondary-foreground: 210 40% 98%;

    --muted: 215 25% 16%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 215 25% 16%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 215 25% 16%;
    --input: 215 25% 16%;
    --ring: 263 70% 50%;

    --radius: 0.5rem;

    --sidebar-background: 5 10% 8%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 263 70% 50%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 215 25% 16%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 25% 16%;
    --sidebar-ring: 263 70% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Space Grotesk', sans-serif;
  }

  /* RTL support - only for Arabic and Persian */
  [dir="rtl"] {
    direction: rtl;
  }

  /* LTR reset - ensure English, French, Spanish stay LTR */
  [dir="ltr"] {
    direction: ltr;
  }

  /* Ensure text alignment stays centered for headings in RTL */
  [dir="rtl"] .text-center {
    text-align: center !important;
  }

  /* Keep centered layouts centered in RTL */
  [dir="rtl"] .mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }

  [dir="rtl"] .text-center h1,
  [dir="rtl"] .text-center h2,
  [dir="rtl"] .text-center h3,
  [dir="rtl"] .text-center h4,
  [dir="rtl"] .text-center h5,
  [dir="rtl"] .text-center h6 {
    text-align: center !important;
  }

  /* Ensure LTR languages maintain proper layout */
  [dir="ltr"] .text-center {
    text-align: center !important;
  }

  [dir="ltr"] .text-left {
    text-align: left !important;
  }

  [dir="ltr"] .text-right {
    text-align: right !important;
  }

  /* Fix hero section spacing for all LTR languages - ensure proper top padding */
  [dir="ltr"] .min-h-screen {
    min-height: 100vh;
    padding-top: 80px; /* Account for fixed header */
  }

  [dir="ltr"] .container {
    margin-left: auto;
    margin-right: auto;
  }

  /* Ensure hero section content doesn't go under nav for all LTR languages */
  [dir="ltr"] section.min-h-screen {
    padding-top: 80px;
  }

  /* Fix for RTL hero section */
  [dir="rtl"] .min-h-screen {
    min-height: 100vh;
    padding-top: 80px;
  }

  [dir="rtl"] section.min-h-screen {
    padding-top: 80px;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-ai-purple via-ai-violet to-ai-blue bg-clip-text text-transparent;
  }
  
  .glow-box {
    box-shadow: 0 0 50px rgba(99, 102, 241, 0.3);
  }
  
  .cyber-grid {
    background-image: 
      linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  .animate-fade-out {
    animation: fadeOut 1s ease-out forwards;
  }

  .animate-fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .animate-fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .animate-slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
  }

  .animate-slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
  }

  .animate-slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
  }

  .animate-slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
  }

  .animate-scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.7s ease-out, transform 0.7s ease-out;
  }

  .animate-scale-in.visible {
    opacity: 1;
    transform: scale(1);
  }

  .animate-stagger {
    transition-delay: var(--delay, 0s);
  }

  /* RTL-specific utilities - only apply when dir="rtl" */
  [dir="rtl"] .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-left: var(--tw-space-x);
    margin-right: 0;
  }

  [dir="rtl"] .rtl\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  /* RTL directional spacing - only for Arabic and Persian */
  [dir="rtl"] .ml-2 { margin-right: 0.5rem; margin-left: 0; }
  [dir="rtl"] .mr-2 { margin-left: 0.5rem; margin-right: 0; }
  [dir="rtl"] .ml-4 { margin-right: 1rem; margin-left: 0; }
  [dir="rtl"] .mr-4 { margin-left: 1rem; margin-right: 0; }
  [dir="rtl"] .ml-8 { margin-right: 2rem; margin-left: 0; }
  [dir="rtl"] .mr-8 { margin-left: 2rem; margin-right: 0; }

  [dir="rtl"] .pl-2 { padding-right: 0.5rem; padding-left: 0; }
  [dir="rtl"] .pr-2 { padding-left: 0.5rem; padding-right: 0; }
  [dir="rtl"] .pl-4 { padding-right: 1rem; padding-left: 0; }
  [dir="rtl"] .pr-4 { padding-left: 1rem; padding-right: 0; }
  [dir="rtl"] .pl-6 { padding-right: 1.5rem; padding-left: 0; }
  [dir="rtl"] .pr-6 { padding-left: 1.5rem; padding-right: 0; }

  [dir="rtl"] .left-0 { right: 0; left: auto; }
  [dir="rtl"] .right-0 { left: 0; right: auto; }
  [dir="rtl"] .left-10 { right: 2.5rem; left: auto; }
  [dir="rtl"] .right-10 { left: 2.5rem; right: auto; }

  /* RTL spacing fixes for contact page */
  [dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    margin-right: 1rem;
    margin-left: 0;
  }

  [dir="rtl"] .space-x-6 > :not([hidden]) ~ :not([hidden]) {
    margin-right: 1.5rem;
    margin-left: 0;
  }

  [dir="rtl"] .gap-4 {
    gap: 1rem;
  }

  [dir="rtl"] .gap-6 {
    gap: 1.5rem;
  }

  /* LTR resets - ensure proper spacing for English, French, Spanish */
  [dir="ltr"] .ml-2 { margin-left: 0.5rem; margin-right: 0; }
  [dir="ltr"] .mr-2 { margin-right: 0.5rem; margin-left: 0; }
  [dir="ltr"] .ml-4 { margin-left: 1rem; margin-right: 0; }
  [dir="ltr"] .mr-4 { margin-right: 1rem; margin-left: 0; }
  [dir="ltr"] .ml-8 { margin-left: 2rem; margin-right: 0; }
  [dir="ltr"] .mr-8 { margin-right: 2rem; margin-left: 0; }

  [dir="ltr"] .pl-2 { padding-left: 0.5rem; padding-right: 0; }
  [dir="ltr"] .pr-2 { padding-right: 0.5rem; padding-left: 0; }
  [dir="ltr"] .pl-4 { padding-left: 1rem; padding-right: 0; }
  [dir="ltr"] .pr-4 { padding-right: 1rem; padding-left: 0; }
  [dir="ltr"] .pl-6 { padding-left: 1.5rem; padding-right: 0; }
  [dir="ltr"] .pr-6 { padding-right: 1.5rem; padding-left: 0; }

  [dir="ltr"] .left-0 { left: 0; right: auto; }
  [dir="ltr"] .right-0 { right: 0; left: auto; }
  [dir="ltr"] .left-10 { left: 2.5rem; right: auto; }
  [dir="ltr"] .right-10 { right: 2.5rem; left: auto; }

  @keyframes fadeOut {
    0% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8) translateY(-20px);
    }
  }
}
