namespace RecoveryRx.Server.DTOs.Database.Responses
{
    /// <summary>
    /// Represents the response for the database connection test.
    /// </summary>
    public class TestConnectionResponse
    {
        /// <summary>
        /// Gets or sets a value indicating whether the connection to the database was successful.
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// Gets or sets the name of the database provider currently in use (e.g., "Sqlite", "MySql").
        /// </summary>
        public string? ProviderName { get; set; }

        /// <summary>
        /// Gets or sets details about the connection (e.g., database file path for SQLite, server/database for MySQL).
        /// Sensitive information like passwords will be omitted.
        /// </summary>
        public string? ConnectionDetails { get; set; }
    }
}
