import Header from "@/components/Header";
import Footer from "@/components/Footer";
import BinaryBackground from "@/components/BinaryBackground";
import MatrixTrail from "@/components/MatrixTrail";
import SEO from "@/components/SEO";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { ShoppingCart, Heart, Rocket, TrendingUp, Users, Clock } from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";

const Portfolio = () => {
  const [headerRef, headerVisible] = useScrollAnimation(0.1);
  const [projectsRef, projectsVisible] = useScrollAnimation(0.1);
  const [ctaRef, ctaVisible] = useScrollAnimation(0.2);
  const [expandedCase, setExpandedCase] = useState<number | null>(null);
  const { t } = useLanguage();

  const caseStudies = [
    {
      icon: <ShoppingCart className="h-10 w-10 text-white" />, // Increased size, white for contrast
      title: t('portfolioPage.ecommerce.title'),
      overview: t('portfolioPage.ecommerce.overview'),
      challenge: t('portfolioPage.ecommerce.challenge'),
      solution: t('portfolioPage.ecommerce.solution'),
      results: t('portfolioPage.ecommerce.results'),
      tech: t('portfolioPage.ecommerce.tech') as unknown as string[],
      gradient: "from-ai-cyan to-ai-blue",
      metrics: [
        { icon: <TrendingUp className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.ecommerce.metrics.conversionRate'), value: "+25%" },
        { icon: <Clock className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.ecommerce.metrics.checkoutSpeed'), value: "+40%" },
        { icon: <Users className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.ecommerce.metrics.costSavings'), value: "35%" }
      ]
    },
    {
      icon: <Heart className="h-10 w-10 text-white" />,
      title: t('portfolioPage.healthcare.title'),
      overview: t('portfolioPage.healthcare.overview'),
      challenge: t('portfolioPage.healthcare.challenge'),
      solution: t('portfolioPage.healthcare.solution'),
      results: t('portfolioPage.healthcare.results'),
      tech: t('portfolioPage.healthcare.tech') as unknown as string[],
      gradient: "from-ai-purple to-ai-violet",
      metrics: [
        { icon: <TrendingUp className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.healthcare.metrics.costReduction'), value: "30%" },
        { icon: <Users className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.healthcare.metrics.engagement'), value: "+20%" },
        { icon: <Clock className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.healthcare.metrics.uptime'), value: "99%" }
      ]
    },
    {
      icon: <Rocket className="h-10 w-10 text-white" />,
      title: t('portfolioPage.startup.title'),
      overview: t('portfolioPage.startup.overview'),
      challenge: t('portfolioPage.startup.challenge'),
      solution: t('portfolioPage.startup.solution'),
      results: t('portfolioPage.startup.results'),
      tech: t('portfolioPage.startup.tech') as unknown as string[],
      gradient: "from-ai-pink to-ai-violet",
      metrics: [
        { icon: <Clock className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.startup.metrics.launchSpeed'), value: "+50%" },
        { icon: <TrendingUp className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.startup.metrics.costSavings'), value: "45%" },
        { icon: <Users className="h-5 w-5 text-ai-cyan" />, label: t('portfolioPage.startup.metrics.weekOneUsers'), value: "10K+" }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background text-foreground relative">
      <SEO 
        title="AI-Powered Portfolio | Maxqit Success Stories - Real Results"
        description="Explore Maxqit's AI-driven successes. See how we delivered 40% faster results, 35% cost savings, and transformative solutions across e-commerce, healthcare, and startups."
        keywords="AI portfolio, software development case studies, AI success stories, cost savings, faster delivery, e-commerce development, healthcare apps, startup solutions"
      />
      
      <BinaryBackground />
      <MatrixTrail />
      <div className="relative z-20">
        <Header />
        
        {/* Hero Section */}
        <section className="py-24">
          <div className="container mx-auto px-6">
            <div 
              ref={headerRef}
              className={`text-center mb-16 transition-all duration-1000 ${headerVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
            >
              <h1 className="text-6xl font-bold mb-6 text-gradient">
                {t('portfolioPage.title')}
              </h1>
              <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-8">
                {t('portfolioPage.subtitle')}
              </p>
              <Button 
                size="lg"
                variant="outline"
                className="border-ai-purple hover:bg-ai-purple/10 px-8 py-4 text-lg"
                onClick={() => document.getElementById('case-studies')?.scrollIntoView({ behavior: 'smooth' })}
              >
                {t('portfolioPage.ctaButton')}
              </Button>
            </div>

            {/* Detailed Case Studies */}
            <div 
              id="case-studies"
              ref={projectsRef}
              className={`space-y-8 mb-16 transition-all duration-1000 delay-300 ${projectsVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
            >
              <h2 className="text-4xl font-bold text-center mb-12 text-gradient">
                {t('portfolioPage.successStoriesTitle')}
              </h2>
              
              {caseStudies.map((study, index) => (
                <Card 
                  key={index}
                  className={`bg-gradient-to-br from-ai-purple/10 to-ai-blue/10 border-ai-purple/30 hover:border-ai-cyan/50 transition-all duration-300 cursor-pointer ${
                    expandedCase === index ? 'ring-2 ring-ai-cyan shadow-lg shadow-ai-cyan/20' : ''
                  }`}
                  onClick={() => setExpandedCase(expandedCase === index ? null : index)}
                >
                  <CardHeader>
                    <div className="flex items-start gap-6">
                      <div className={`w-16 h-16 rounded-lg bg-gradient-to-r ${study.gradient} flex items-center justify-center shadow-lg flex-shrink-0 relative`}>
                        {/* Add a subtle dark overlay for better contrast */}
                        <div className="absolute inset-0 bg-black/20 rounded-lg"></div>
                        <div className="relative z-10">
                          {study.icon}
                        </div>
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-2xl mb-2">{study.title}</CardTitle>
                        <CardDescription className="text-lg">
                          {study.overview}
                        </CardDescription>
                        <div className="flex flex-wrap gap-4 mt-4">
                          {study.metrics.map((metric, metricIndex) => (
                            <div key={metricIndex} className="flex items-center gap-2 text-ai-cyan">
                              <div className="relative">
                                {metric.icon}
                              </div>
                              <span className="text-sm font-semibold">{metric.label}: {metric.value}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  {expandedCase === index && (
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                        <div className="bg-gradient-to-br from-red-500/10 to-red-600/10 rounded-lg p-6 border border-red-500/20">
                          <h4 className="text-lg font-bold text-red-400 mb-3">{t('portfolioPage.challenge')}</h4>
                          <p className="text-sm leading-relaxed">{study.challenge}</p>
                        </div>
                        
                        <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 rounded-lg p-6 border border-yellow-500/20">
                          <h4 className="text-lg font-bold text-yellow-400 mb-3">{t('portfolioPage.solution')}</h4>
                          <p className="text-sm leading-relaxed">{study.solution}</p>
                        </div>
                        
                        <div className="bg-gradient-to-br from-green-500/10 to-green-600/10 rounded-lg p-6 border border-green-500/20">
                          <h4 className="text-lg font-bold text-green-400 mb-3">{t('portfolioPage.results')}</h4>
                          <p className="text-sm leading-relaxed">{study.results}</p>
                        </div>
                      </div>
                      
                      <div className="mt-6 bg-card/50 rounded-lg p-6 border border-ai-purple/20">
                        <h4 className="text-lg font-bold text-ai-cyan mb-3">{t('portfolioPage.technologiesUsed')}</h4>
                        <div className="flex flex-wrap gap-2">
                          {study.tech.map((tech, techIndex) => (
                            <span 
                              key={techIndex}
                              className="px-3 py-1 bg-ai-purple/20 rounded-full text-sm border border-ai-purple/30"
                            >
                              {tech.trim()}
                            </span>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>

            {/* CTA Section */}
            <div 
              ref={ctaRef}
              className={`text-center transition-all duration-1000 delay-600 ${ctaVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
            >
              <h2 className="text-4xl font-bold mb-6 text-gradient">
                {t('portfolioPage.yourSuccessStory')}
              </h2>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                {t('portfolioPage.yourSuccessSubtitle')}
              </p>
              <Link to="/contact">
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-ai-cyan to-ai-blue hover:from-ai-blue hover:to-ai-cyan transition-all duration-300 px-8 py-4 text-lg"
                >
                  {t('portfolioPage.getFreeQuote')}
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default Portfolio;