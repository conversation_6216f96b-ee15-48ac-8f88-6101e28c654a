using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RecoveryRx.Models;

public class UserRole
{
    [Key]
    public Guid UserId { get; set; }
    [ForeignKey("UserId")]
    public User User { get; set; } = null!;

    [Key]
    public Guid RoleId { get; set; }
    [ForeignKey("RoleId")]
    public Role Role { get; set; } = null!;
}
